# 🚨 紧急修复指南 - 数据库表缺失问题

## 🔍 问题诊断
根据错误日志，主要问题是：
```
relation "public.user_subscriptions" does not exist
```

这意味着Supabase数据库中缺少必要的表结构。

## 🛠️ 立即修复步骤

### 步骤1：登录Supabase
1. 访问：https://supabase.com/dashboard
2. 选择您的项目：`elwmthdxnsyzfvlunvmo`

### 步骤2：执行数据库设置
1. 点击左侧菜单 **"SQL Editor"**
2. 点击 **"New Query"**
3. 复制 `SUPABASE_SETUP.sql` 文件的全部内容
4. 粘贴到SQL编辑器中
5. 点击 **"Run"** 按钮执行

### 步骤3：验证表创建
1. 点击左侧菜单 **"Table Editor"**
2. 确认看到以下表：
   - `user_subscriptions`
   - `payment_history` 
   - `reading_history`

### 步骤4：测试应用
1. 访问：https://mystic-card-mirror-vip.lovable.app/payment/test
2. 登录您的账户
3. 点击 **"Test Manual Upgrade"**
4. 检查是否成功激活订阅

## 🎯 预期结果

执行SQL后，您应该看到：
- ✅ 3个表成功创建
- ✅ 现有用户自动获得订阅记录
- ✅ 新用户注册时自动创建订阅
- ✅ 支付成功页面能正常激活会员

## 🔧 如果仍有问题

### 检查表是否存在
在SQL编辑器中运行：
```sql
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('user_subscriptions', 'payment_history', 'reading_history');
```

### 检查用户订阅记录
```sql
SELECT * FROM public.user_subscriptions LIMIT 5;
```

### 手动创建测试订阅
```sql
INSERT INTO public.user_subscriptions (
    user_id,
    subscription_type,
    status,
    daily_questions_limit
) VALUES (
    'your-user-id-here',
    'monthly',
    'active',
    999
);
```

## 📞 联系支持

如果问题持续存在：
1. 检查Supabase项目权限
2. 确认使用正确的项目URL
3. 验证API密钥配置

## 🎉 成功标志

修复成功后，您应该能够：
- ✅ 访问 `/payment/test` 页面无错误
- ✅ 看到用户订阅信息
- ✅ 手动激活Premium订阅
- ✅ 支付流程正常工作

**立即执行 `SUPABASE_SETUP.sql` 即可解决所有数据库问题！** 🚀
