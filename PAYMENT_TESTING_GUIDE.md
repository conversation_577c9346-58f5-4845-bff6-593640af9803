# 💳 支付功能测试指南

## 🎯 测试产品信息

### Creem.io 测试产品
- **产品ID：** `prod_2Ei8CdkAewpPbGZFz09Cgq`
- **产品链接：** https://www.creem.io/test/payment/prod_2Ei8CdkAewpPbGZFz09Cgq
- **价格：** $4.99/月
- **类型：** 月度订阅

## 🔗 集成状态

✅ **已完成的集成：**
- 定价页面支付按钮已连接到测试产品
- 支付成功/取消页面已配置
- Webhook处理逻辑已实现
- 用户订阅状态管理已集成

## 🧪 修复后的测试流程

### 1. 准备测试环境
```bash
# 确保开发服务器运行
npm run dev
# 访问 http://localhost:8080
```

### 2. 修复后的支付流程

#### 步骤 1：访问定价页面
- 访问：http://localhost:8080/pricing
- 或点击导航栏的"Pricing"链接

#### 步骤 2：用户登录
- 如果未登录，先注册/登录账户
- 确保用户邮箱地址有效

#### 步骤 3：开始支付
- 点击"Upgrade Now"按钮
- 系统现在会：
  1. 调用Creem.io API创建checkout session
  2. 获取支付URL
  3. 重定向到Creem.io支付页面

#### 步骤 4：完成支付
- 在Creem.io页面完成测试支付
- 使用测试信用卡信息（如果需要）

#### 步骤 5：验证结果
- **支付成功：** 重定向到 `/payment/success` 并带有查询参数：
  ```
  ?checkout_id=ch_xxx&order_id=ord_xxx&customer_id=cust_xxx&
  subscription_id=sub_xxx&product_id=prod_xxx&request_id=user_id&signature=xxx
  ```
- **支付取消：** 重定向到 `/payment/cancel`

## 🔧 关键修复

### 1. 正确的API集成
- ✅ 使用 `https://api.creem.io/v1/checkouts` 创建支付会话
- ✅ 正确的API密钥头部：`x-api-key`
- ✅ 包含 `request_id` 用于跟踪用户

### 2. 返回URL处理
- ✅ 正确解析Creem.io返回的查询参数
- ✅ 使用 `checkout_id`, `order_id`, `request_id` 等参数
- ✅ 验证支付完成状态

### 3. Webhook事件
- ✅ 更新为正确的事件类型：`order.completed`, `subscription.created` 等
- ✅ 处理Creem.io的事件结构

### 3. 验证Webhook事件

#### 检查Webhook接收
- 支付完成后，Creem.io会发送Webhook到：
```
https://mystic-card-mirror-vip.lovable.app/api/webhooks/creem
```

#### 验证数据库更新
- 检查 `user_subscriptions` 表
- 确认用户订阅状态更新为 `active`
- 验证 `payment_history` 记录

## 🔍 测试检查清单

### 支付流程测试
- [ ] 定价页面加载正常
- [ ] 升级按钮正确重定向到Creem.io
- [ ] 支付页面显示正确的产品信息
- [ ] 支付成功后正确重定向
- [ ] 支付取消后正确重定向

### 用户体验测试
- [ ] 未登录用户被引导到登录页面
- [ ] 已登录用户可以直接支付
- [ ] 支付成功页面显示正确信息
- [ ] 用户订阅状态正确更新

### Webhook测试
- [ ] Webhook URL配置正确
- [ ] 支付成功事件正确处理
- [ ] 用户订阅状态自动更新
- [ ] 支付历史记录正确保存

### 订阅功能测试
- [ ] 免费用户每天1个问题限制
- [ ] 试用用户3天无限制访问
- [ ] 付费用户无限制访问
- [ ] 订阅状态在界面正确显示

## 🛠️ 调试工具

### 1. 浏览器开发者工具
```javascript
// 检查支付URL生成
console.log('Payment URL:', paymentUrl);

// 检查用户信息
console.log('User:', user);

// 检查订阅状态
console.log('Subscription:', subscription);
```

### 2. 网络请求监控
- 监控支付重定向
- 检查Webhook请求
- 验证API调用

### 3. 数据库查询
```sql
-- 检查用户订阅状态
SELECT * FROM user_subscriptions WHERE user_id = 'user_uuid';

-- 检查支付历史
SELECT * FROM payment_history WHERE user_id = 'user_uuid';
```

## 🚨 常见问题排查

### 支付重定向失败
- 检查产品ID是否正确
- 验证success_url和cancel_url编码
- 确认用户邮箱和ID传递正确

### Webhook未接收
- 验证Webhook URL配置
- 检查Creem.io仪表板的发送日志
- 确认服务器可以接收POST请求

### 订阅状态未更新
- 检查Webhook处理逻辑
- 验证数据库连接
- 确认用户ID匹配

## 📊 测试数据示例

### 测试用户信息
```json
{
  "id": "test_user_123",
  "email": "<EMAIL>",
  "subscription_type": "free_trial",
  "status": "expired"
}
```

### 预期Webhook事件
```json
{
  "type": "checkout.session.completed",
  "data": {
    "object": {
      "id": "cs_test_123",
      "amount_total": 499,
      "currency": "usd",
      "customer": {
        "email": "<EMAIL>"
      },
      "metadata": {
        "user_id": "test_user_123"
      }
    }
  }
}
```

## 🎯 测试目标

### 功能验证
- ✅ 支付流程完整可用
- ✅ 用户订阅状态正确管理
- ✅ Webhook事件正确处理
- ✅ 使用限制正确实施

### 用户体验
- ✅ 支付流程简单直观
- ✅ 错误处理友好
- ✅ 状态反馈及时
- ✅ 界面响应迅速

## 📞 测试支持

如果在测试过程中遇到问题：

1. **检查控制台错误**
2. **验证网络请求**
3. **查看数据库状态**
4. **联系技术支持：** <EMAIL>

## 🚀 部署前最终检查

- [ ] 所有测试用例通过
- [ ] Webhook配置正确
- [ ] 数据库表结构完整
- [ ] 错误处理完善
- [ ] 用户界面友好

测试完成后，您的支付系统就可以正式上线了！🎉
