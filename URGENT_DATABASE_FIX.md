# 🚨 紧急数据库修复指南

## 🔍 问题诊断

### 当前问题：
1. **缺失数据库函数** - `can_user_read_tarot` 和 `record_tarot_usage` 函数不存在
2. **新用户试用期失败** - 用户注册后显示为 `free` 而不是 `free_trial`
3. **订阅状态错误** - `trial_end_date: null` 说明没有设置试用期

### 错误日志分析：
```
❌ Error checking tarot permission: {
  code: '42703', 
  details: null, 
  hint: null, 
  message: 'record "user_profile" has no field "subscription_status"'
}
```

## 🔧 立即修复步骤

### 步骤1: 执行数据库修复脚本

1. **登录Supabase Dashboard**
   ```
   访问：https://elwmthdxnsyzfvlunvmo.supabase.co
   ```

2. **打开SQL Editor**
   - 点击左侧菜单 "SQL Editor"
   - 点击 "New query"

3. **执行修复脚本**
   - 复制 `database_functions_fix.sql` 的全部内容
   - 粘贴到SQL Editor中
   - 点击 "Run" 执行

### 步骤2: 验证修复结果

#### 检查函数是否创建成功：
```sql
-- 检查函数是否存在
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('can_user_read_tarot', 'record_tarot_usage', 'handle_new_user');
```

#### 检查用户订阅状态：
```sql
-- 检查特定用户的订阅状态
SELECT 
    user_id,
    subscription_type,
    status,
    daily_questions_limit,
    trial_start_date,
    trial_end_date,
    trial_used
FROM user_subscriptions 
WHERE user_id = 'b454cbd9-934f-4015-a220-8074e279a805';
```

#### 测试函数是否工作：
```sql
-- 测试 can_user_read_tarot 函数
SELECT can_user_read_tarot('b454cbd9-934f-4015-a220-8074e279a805');
```

### 步骤3: 手动修复特定用户（如果需要）

如果自动修复没有生效，手动修复用户：
```sql
-- 手动给用户3天试用期
UPDATE user_subscriptions 
SET 
    subscription_type = 'free_trial',
    status = 'active',
    daily_questions_limit = 20,
    trial_start_date = NOW(),
    trial_end_date = NOW() + INTERVAL '3 days',
    trial_used = true,
    updated_at = NOW()
WHERE user_id = 'b454cbd9-934f-4015-a220-8074e279a805';
```

## 🎯 预期修复结果

### 修复后的用户状态应该是：
```json
{
  "subscription_type": "free_trial",
  "status": "active",
  "daily_questions_limit": 20,
  "daily_questions_used": 0,
  "trial_start_date": "2024-12-XX...",
  "trial_end_date": "2024-12-XX...",
  "trial_used": true
}
```

### 修复后的函数调用应该成功：
```
✅ can_user_read_tarot 返回 true
✅ record_tarot_usage 正常执行
✅ 网站不再报错
```

## 🚀 测试验证

### 1. 网站测试
```
1. 刷新网站页面
2. 检查控制台是否还有错误
3. 尝试进行塔罗占卜
4. 检查用户状态显示
```

### 2. 预期日志
```
✅ Auth state changed: SIGNED_IN <EMAIL>
✅ Subscription Status Debug: {
  subscription_type: "free_trial",
  status: "active",
  trial_end_date: "2024-12-XX...",
  daily_questions_limit: 20
}
✅ Status Calculation Results: {
  hasActiveTrial: true,
  isTrialActive: true
}
```

## 🔄 如果问题仍然存在

### 检查清单：
- [ ] SQL脚本是否完全执行成功？
- [ ] 函数是否正确创建？
- [ ] 用户订阅记录是否更新？
- [ ] 网站是否重新部署？

### 备用方案：
如果自动修复失败，可以：

1. **重新创建用户订阅记录**：
```sql
DELETE FROM user_subscriptions WHERE user_id = 'b454cbd9-934f-4015-a220-8074e279a805';

INSERT INTO user_subscriptions (
    user_id,
    subscription_type,
    status,
    daily_questions_limit,
    daily_questions_used,
    trial_start_date,
    trial_end_date,
    trial_used
) VALUES (
    'b454cbd9-934f-4015-a220-8074e279a805',
    'free_trial',
    'active',
    20,
    0,
    NOW(),
    NOW() + INTERVAL '3 days',
    true
);
```

2. **检查RLS策略**：
```sql
-- 确保RLS策略允许用户访问自己的数据
SELECT * FROM pg_policies WHERE tablename = 'user_subscriptions';
```

## 📞 紧急联系

如果修复后仍有问题，请提供：
1. **SQL执行结果截图**
2. **用户订阅状态查询结果**
3. **网站控制台完整错误日志**
4. **函数测试结果**

---

**请立即执行数据库修复脚本，这将解决所有当前问题！** 🚨🔧✅
