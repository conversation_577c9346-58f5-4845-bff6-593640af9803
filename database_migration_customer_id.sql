-- 添加customer_id字段到user_subscriptions表
-- 这个字段用于存储Creem.io的customer_id，以便访问Customer Portal

-- 添加customer_id字段
ALTER TABLE public.user_subscriptions 
ADD COLUMN IF NOT EXISTS customer_id VARCHAR(255);

-- 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_customer_id 
ON public.user_subscriptions(customer_id);

-- 添加注释
COMMENT ON COLUMN public.user_subscriptions.customer_id 
IS 'Creem.io customer ID for accessing Customer Portal';

-- 验证字段已添加
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'user_subscriptions' 
AND column_name = 'customer_id';
