-- 修复数据库函数
-- 创建缺失的 can_user_read_tarot 和 record_tarot_usage 函数

-- 1. 创建 can_user_read_tarot 函数
CREATE OR REPLACE FUNCTION can_user_read_tarot(user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    subscription_record user_subscriptions%ROWTYPE;
    today_date DATE;
    last_question_date DATE;
BEGIN
    -- 获取用户订阅信息
    SELECT * INTO subscription_record 
    FROM user_subscriptions 
    WHERE user_id = user_uuid;
    
    -- 如果没有订阅记录，返回false
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- 如果是付费用户且状态为active，返回true
    IF subscription_record.subscription_type = 'monthly' AND subscription_record.status = 'active' THEN
        -- 检查订阅是否过期
        IF subscription_record.subscription_end_date IS NOT NULL AND NOW() > subscription_record.subscription_end_date THEN
            RETURN FALSE;
        END IF;
        RETURN TRUE;
    END IF;
    
    -- 如果是试用期用户且在试用期内，返回true
    IF subscription_record.subscription_type = 'free_trial' AND subscription_record.status = 'active' THEN
        IF subscription_record.trial_end_date IS NOT NULL AND NOW() <= subscription_record.trial_end_date THEN
            RETURN TRUE;
        END IF;
    END IF;
    
    -- 免费用户检查每日限制
    today_date := CURRENT_DATE;
    
    -- 如果last_question_date为空或者不是今天，可以提问
    IF subscription_record.last_question_date IS NULL THEN
        RETURN TRUE;
    END IF;
    
    last_question_date := DATE(subscription_record.last_question_date);
    
    -- 如果不是今天，可以提问
    IF last_question_date != today_date THEN
        RETURN TRUE;
    END IF;
    
    -- 检查今日使用次数
    RETURN subscription_record.daily_questions_used < subscription_record.daily_questions_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. 创建 record_tarot_usage 函数
CREATE OR REPLACE FUNCTION record_tarot_usage(user_uuid UUID)
RETURNS VOID AS $$
DECLARE
    subscription_record user_subscriptions%ROWTYPE;
    today_date DATE;
    last_question_date DATE;
    new_count INTEGER;
BEGIN
    -- 获取用户订阅信息
    SELECT * INTO subscription_record 
    FROM user_subscriptions 
    WHERE user_id = user_uuid;
    
    -- 如果没有订阅记录，退出
    IF NOT FOUND THEN
        RETURN;
    END IF;
    
    today_date := CURRENT_DATE;
    
    -- 如果last_question_date为空或者不是今天，重置计数
    IF subscription_record.last_question_date IS NULL THEN
        new_count := 1;
    ELSE
        last_question_date := DATE(subscription_record.last_question_date);
        IF last_question_date != today_date THEN
            new_count := 1;
        ELSE
            new_count := subscription_record.daily_questions_used + 1;
        END IF;
    END IF;
    
    -- 更新记录
    UPDATE user_subscriptions 
    SET 
        daily_questions_used = new_count,
        last_question_date = NOW(),
        updated_at = NOW()
    WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. 修复新用户触发器，确保创建3天试用期
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    trial_end_date TIMESTAMPTZ;
BEGIN
    -- 计算3天试用期结束时间
    trial_end_date := NOW() + INTERVAL '3 days';
    
    -- 为新用户创建3天试用期订阅
    INSERT INTO public.user_subscriptions (
        user_id,
        subscription_type,
        status,
        daily_questions_limit,
        daily_questions_used,
        trial_start_date,
        trial_end_date,
        trial_used,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        'free_trial',
        'active',
        20,  -- 试用期每天20次
        0,
        NOW(),
        trial_end_date,
        true,
        NOW(),
        NOW()
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. 重新创建触发器
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- 5. 修复现有的免费用户，给他们3天试用期
UPDATE user_subscriptions 
SET 
    subscription_type = 'free_trial',
    status = 'active',
    daily_questions_limit = 20,
    trial_start_date = NOW(),
    trial_end_date = NOW() + INTERVAL '3 days',
    trial_used = true,
    updated_at = NOW()
WHERE 
    subscription_type = 'free' 
    AND status = 'active' 
    AND trial_used IS NOT TRUE
    AND (trial_end_date IS NULL OR trial_end_date < NOW());

-- 6. 创建一个函数来检查和修复用户订阅状态
CREATE OR REPLACE FUNCTION fix_user_subscription_status()
RETURNS TABLE(user_id UUID, old_type TEXT, new_type TEXT, message TEXT) AS $$
BEGIN
    RETURN QUERY
    WITH updated_users AS (
        UPDATE user_subscriptions 
        SET 
            subscription_type = 'free_trial',
            status = 'active',
            daily_questions_limit = 20,
            trial_start_date = COALESCE(trial_start_date, NOW()),
            trial_end_date = COALESCE(trial_end_date, NOW() + INTERVAL '3 days'),
            trial_used = true,
            updated_at = NOW()
        WHERE 
            subscription_type = 'free' 
            AND status = 'active' 
            AND (trial_used IS NOT TRUE OR trial_used IS NULL)
            AND (trial_end_date IS NULL OR trial_end_date < NOW())
        RETURNING 
            user_subscriptions.user_id,
            'free' as old_subscription_type,
            subscription_type as new_subscription_type
    )
    SELECT 
        u.user_id,
        u.old_subscription_type,
        u.new_subscription_type,
        '已修复为3天试用期' as message
    FROM updated_users u;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 执行修复
SELECT * FROM fix_user_subscription_status();

-- 7. 授予必要的权限
GRANT EXECUTE ON FUNCTION can_user_read_tarot(UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION record_tarot_usage(UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION fix_user_subscription_status() TO service_role;
