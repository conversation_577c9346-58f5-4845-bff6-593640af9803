-- 🚀 Mystic Card Mirror VIP - Complete Supabase Setup
-- Execute this SQL in your Supabase SQL Editor: https://supabase.com/dashboard/project/elwmthdxnsyzfvlunvmo/sql

-- ============================================================================
-- 1. CREATE TABLES
-- ============================================================================

-- Drop existing tables if they exist (for clean setup)
DROP TABLE IF EXISTS public.reading_history CASCADE;
DROP TABLE IF EXISTS public.payment_history CASCADE;
DROP TABLE IF EXISTS public.user_subscriptions CASCADE;

-- Create user_subscriptions table
CREATE TABLE public.user_subscriptions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    subscription_type VARCHAR(50) NOT NULL DEFAULT 'free',
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    subscription_start_date TIMESTAMPTZ DEFAULT NOW(),
    subscription_end_date TIMESTAMPTZ,
    trial_start_date TIMESTAMPTZ,
    trial_end_date TIMESTAMPTZ,
    trial_used BOOLEAN DEFAULT FALSE,
    daily_questions_used INTEGER DEFAULT 0,
    daily_questions_limit INTEGER DEFAULT 1,
    last_question_date DATE,
    subscription_id VARCHAR(255), -- Creem.io subscription ID
    current_period_start TIMESTAMPTZ,
    current_period_end TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Create payment_history table
CREATE TABLE public.payment_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    order_id VARCHAR(255),
    subscription_id VARCHAR(255),
    amount DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(20) NOT NULL,
    payment_method VARCHAR(50),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create reading_history table
CREATE TABLE public.reading_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    question TEXT NOT NULL,
    cards JSONB NOT NULL,
    ai_reading TEXT NOT NULL,
    spread_type VARCHAR(50) DEFAULT 'three_card',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create reading_feedback table
CREATE TABLE public.reading_feedback (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    reading_id UUID NOT NULL REFERENCES public.reading_history(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    accuracy_rating INTEGER CHECK (accuracy_rating >= 1 AND accuracy_rating <= 5),
    satisfaction_rating INTEGER CHECK (satisfaction_rating >= 1 AND satisfaction_rating <= 5),
    usefulness_rating INTEGER CHECK (usefulness_rating >= 1 AND usefulness_rating <= 5),
    feedback_text TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(reading_id, user_id)
);

-- ============================================================================
-- 2. CREATE INDEXES
-- ============================================================================

CREATE INDEX idx_user_subscriptions_user_id ON public.user_subscriptions(user_id);
CREATE INDEX idx_payment_history_user_id ON public.payment_history(user_id);
CREATE INDEX idx_reading_history_user_id ON public.reading_history(user_id);
CREATE INDEX idx_reading_feedback_user_id ON public.reading_feedback(user_id);
CREATE INDEX idx_reading_feedback_reading_id ON public.reading_feedback(reading_id);
CREATE INDEX idx_user_subscriptions_status ON public.user_subscriptions(status);
CREATE INDEX idx_user_subscriptions_type ON public.user_subscriptions(subscription_type);

-- ============================================================================
-- 3. ENABLE ROW LEVEL SECURITY
-- ============================================================================

ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reading_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reading_feedback ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- 4. CREATE RLS POLICIES
-- ============================================================================

-- Policies for user_subscriptions
CREATE POLICY "Users can view own subscription" ON public.user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own subscription" ON public.user_subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own subscription" ON public.user_subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

-- Policies for payment_history
CREATE POLICY "Users can view own payment history" ON public.payment_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own payment history" ON public.payment_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policies for reading_history
CREATE POLICY "Users can view own reading history" ON public.reading_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own reading history" ON public.reading_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policies for reading_feedback
CREATE POLICY "Users can view own reading feedback" ON public.reading_feedback
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own reading feedback" ON public.reading_feedback
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own reading feedback" ON public.reading_feedback
    FOR UPDATE USING (auth.uid() = user_id);

-- ============================================================================
-- 5. CREATE FUNCTIONS
-- ============================================================================

-- Function to automatically create subscription for new users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_subscriptions (
        user_id,
        subscription_type,
        status,
        daily_questions_limit,
        daily_questions_used,
        trial_used
    ) VALUES (
        NEW.id,
        'free',
        'active',
        1,  -- Free users: 1 question per day
        0,
        FALSE
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- 6. CREATE TRIGGERS
-- ============================================================================

-- Trigger to create subscription for new users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Trigger to update updated_at
DROP TRIGGER IF EXISTS update_user_subscriptions_updated_at ON public.user_subscriptions;
CREATE TRIGGER update_user_subscriptions_updated_at
    BEFORE UPDATE ON public.user_subscriptions
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- ============================================================================
-- 7. GRANT PERMISSIONS
-- ============================================================================

GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.user_subscriptions TO anon, authenticated;
GRANT ALL ON public.payment_history TO anon, authenticated;
GRANT ALL ON public.reading_history TO anon, authenticated;
GRANT ALL ON public.reading_feedback TO anon, authenticated;

-- ============================================================================
-- 8. CREATE TEST DATA (for existing users)
-- ============================================================================

-- Insert subscription record for existing users who don't have one
INSERT INTO public.user_subscriptions (
    user_id,
    subscription_type,
    status,
    daily_questions_limit,
    daily_questions_used,
    trial_used
)
SELECT
    id,
    'free',
    'active',
    1,  -- Free users: 1 question per day
    0,
    FALSE
FROM auth.users
WHERE id NOT IN (SELECT user_id FROM public.user_subscriptions)
ON CONFLICT (user_id) DO NOTHING;

-- Update any existing monthly subscriptions to have correct daily limit
UPDATE public.user_subscriptions
SET daily_questions_limit = 20
WHERE subscription_type IN ('monthly', 'premium') AND daily_questions_limit != 20;

-- Update any existing trial subscriptions to have correct daily limit
UPDATE public.user_subscriptions
SET daily_questions_limit = 20
WHERE subscription_type = 'free_trial' AND daily_questions_limit != 20;

-- ============================================================================
-- SETUP COMPLETE!
-- ============================================================================

-- Next steps:
-- 1. Execute this SQL in Supabase SQL Editor
-- 2. Verify tables are created: Go to Table Editor
-- 3. Test the application
-- 4. Check that new users automatically get subscription records

SELECT 'Database setup completed successfully!' as message;
