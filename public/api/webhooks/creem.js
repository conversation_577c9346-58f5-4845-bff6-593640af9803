// Creem.io Webhook处理端点
// 注意：这是一个示例文件，实际部署时需要在后端服务器上实现

// 这个文件展示了如何处理Creem.io的Webhook事件
// 在实际部署中，您需要：
// 1. 在后端服务器（如Vercel Functions、Netlify Functions等）上实现这个逻辑
// 2. 确保有正确的CORS设置
// 3. 验证Webhook签名
// 4. 连接到Supabase数据库

const SUPABASE_URL = 'YOUR_SUPABASE_URL';
const SUPABASE_SERVICE_KEY = 'YOUR_SUPABASE_SERVICE_KEY';
const CREEM_API_KEY = 'creem_2gPDtvmta7jlXaykq0H9Is';

// 处理Webhook请求的主函数
async function handleWebhook(request) {
  try {
    // 验证请求方法
    if (request.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 });
    }

    // 获取请求体
    const body = await request.text();
    const event = JSON.parse(body);

    console.log('Received Creem webhook event:', event.type);
    console.log('Event data:', event.data);

    // 验证Webhook签名（可选但推荐）
    const signature = request.headers.get('x-creem-signature');
    if (!verifySignature(body, signature, CREEM_API_KEY)) {
      console.error('Invalid webhook signature');
      return new Response('Invalid signature', { status: 401 });
    }

    // 处理不同类型的事件
    let result;
    switch (event.type) {
      case 'checkout.completed':
      case 'order.completed':
      case 'subscription.created':
        result = await handlePaymentSuccess(event.data);
        break;
      
      case 'subscription.cancelled':
      case 'subscription.expired':
        result = await handleSubscriptionCancelled(event.data);
        break;
      
      case 'checkout.failed':
      case 'order.failed':
        result = await handlePaymentFailed(event.data);
        break;
      
      default:
        console.log('Unhandled event type:', event.type);
        result = { success: true, message: 'Event logged' };
    }

    // 返回成功响应
    return new Response(JSON.stringify(result), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Webhook processing error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// 处理支付成功事件
async function handlePaymentSuccess(eventData) {
  const { object } = eventData;
  const userId = object.request_id || object.metadata?.userId;
  
  if (!userId) {
    throw new Error('No user ID found in payment event');
  }

  console.log('Processing payment success for user:', userId);

  // 更新Supabase中的用户订阅状态
  const supabaseResponse = await fetch(`${SUPABASE_URL}/rest/v1/user_subscriptions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
      'apikey': SUPABASE_SERVICE_KEY,
      'Prefer': 'resolution=merge-duplicates'
    },
    body: JSON.stringify({
      user_id: userId,
      subscription_type: 'monthly',
      status: 'active',
      subscription_id: object.subscription?.id,
      current_period_start: object.subscription?.current_period_start,
      current_period_end: object.subscription?.current_period_end,
      subscription_start_date: new Date().toISOString(),
      subscription_end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30天后
      daily_questions_used: 0,
      daily_questions_limit: 999, // 付费用户无限制
      updated_at: new Date().toISOString()
    })
  });

  if (!supabaseResponse.ok) {
    const error = await supabaseResponse.text();
    throw new Error(`Failed to update subscription: ${error}`);
  }

  // 记录支付历史
  if (object.order) {
    await recordPaymentHistory(userId, object.order);
  }

  return { success: true, message: 'Subscription activated' };
}

// 处理订阅取消事件
async function handleSubscriptionCancelled(eventData) {
  const { object } = eventData;
  const userId = object.metadata?.userId;
  
  if (!userId) {
    throw new Error('No user ID found in cancellation event');
  }

  console.log('Processing subscription cancellation for user:', userId);

  // 更新订阅状态为取消
  const supabaseResponse = await fetch(`${SUPABASE_URL}/rest/v1/user_subscriptions?user_id=eq.${userId}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
      'apikey': SUPABASE_SERVICE_KEY
    },
    body: JSON.stringify({
      status: 'cancelled',
      subscription_end_date: object.current_period_end || new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
  });

  if (!supabaseResponse.ok) {
    const error = await supabaseResponse.text();
    throw new Error(`Failed to cancel subscription: ${error}`);
  }

  return { success: true, message: 'Subscription cancelled' };
}

// 处理支付失败事件
async function handlePaymentFailed(eventData) {
  const { object } = eventData;
  console.log('Payment failed:', object);
  
  // 可以在这里记录失败的支付尝试
  // 或发送通知给用户
  
  return { success: true, message: 'Payment failure logged' };
}

// 记录支付历史
async function recordPaymentHistory(userId, orderData) {
  const supabaseResponse = await fetch(`${SUPABASE_URL}/rest/v1/payment_history`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
      'apikey': SUPABASE_SERVICE_KEY
    },
    body: JSON.stringify({
      user_id: userId,
      order_id: orderData.id,
      amount: orderData.amount,
      currency: orderData.currency,
      status: 'completed',
      created_at: new Date().toISOString()
    })
  });

  if (!supabaseResponse.ok) {
    console.error('Failed to record payment history');
  }
}

// 验证Webhook签名
function verifySignature(payload, signature, apiKey) {
  // 这里应该实现实际的签名验证逻辑
  // 根据Creem.io的文档实现HMAC验证
  return true; // 简化版本，实际应用中需要真正的验证
}

// 导出处理函数（用于Vercel、Netlify等平台）
export default handleWebhook;

// 如果在Node.js环境中使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = handleWebhook;
}

// 使用说明：
// 1. 将此文件部署到支持serverless functions的平台
// 2. 在Creem.io仪表板中配置Webhook URL
// 3. 确保环境变量正确设置
// 4. 测试Webhook接收和处理
