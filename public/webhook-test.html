<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Webhook Test Endpoint</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .endpoint-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #4CAF50;
        }
        .code {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border-left: 4px solid #FFC107;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .success {
            background: rgba(76, 175, 80, 0.2);
            border-left: 4px solid #4CAF50;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        h1, h2 {
            color: #fff;
        }
        .copy-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 10px;
        }
        .copy-btn:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔮 Mystic Card Mirror - Webhook Configuration</h1>

        <div class="success">
            <h2>✅ Webhook Endpoint Ready</h2>
            <p>Your webhook endpoint is configured and ready to receive Creem.io events.</p>
        </div>

        <div class="endpoint-info">
            <h2>📡 Webhook URL</h2>
            <div class="code">
                <span id="webhook-url">https://mystic-card-mirror-vip.lovable.app/api/webhooks/creem</span>
                <button class="copy-btn" onclick="copyToClipboard('webhook-url')">Copy</button>
            </div>
            <p><strong>Note:</strong> This is your production webhook URL.</p>
        </div>

        <div class="endpoint-info">
            <h2>🎯 Supported Events</h2>
            <div class="code">
checkout.session.completed
checkout.session.failed
subscription.cancelled
payment.succeeded
payment.failed
            </div>
        </div>

        <div class="endpoint-info">
            <h2>⚙️ Configuration Details</h2>
            <ul>
                <li><strong>Method:</strong> POST</li>
                <li><strong>Content-Type:</strong> application/json</li>
                <li><strong>Authentication:</strong> Signature verification enabled</li>
                <li><strong>Timeout:</strong> 30 seconds</li>
            </ul>
        </div>

        <div class="warning">
            <h2>⚠️ Important Setup Steps</h2>
            <ol>
                <li>Copy the webhook URL above</li>
                <li>Go to your Creem.io dashboard</li>
                <li>Navigate to Webhooks settings</li>
                <li>Add a new webhook with the URL above</li>
                <li>Select all the events listed above</li>
                <li>Save the configuration</li>
            </ol>
        </div>

        <div class="endpoint-info">
            <h2>🔧 API Configuration</h2>
            <p><strong>Creem.io API Key:</strong></p>
            <div class="code">
                creem_2gPDtvmta7jlXaykq0H9Is
            </div>

            <p><strong>Hugging Face API Key:</strong></p>
            <div class="code">
                *************************************
            </div>
        </div>

        <div class="endpoint-info">
            <h2>🛒 Test Product Information</h2>
            <p><strong>Test Product ID:</strong></p>
            <div class="code">
                prod_2Ei8CdkAewpPbGZFz09Cgq
            </div>
            <p><strong>Test Product URL:</strong></p>
            <div class="code">
                https://www.creem.io/test/payment/prod_2Ei8CdkAewpPbGZFz09Cgq
            </div>
        </div>

        <div class="endpoint-info">
            <h2>💰 Subscription Plans</h2>
            <ul>
                <li><strong>Free Trial:</strong> 3 days unlimited access</li>
                <li><strong>Monthly Premium:</strong> $4.99/month unlimited access</li>
                <li><strong>Free Tier:</strong> 1 question per day</li>
            </ul>
        </div>

        <div class="endpoint-info">
            <h2>📞 Support Information</h2>
            <ul>
                <li><strong>Email:</strong> <EMAIL></li>
                <li><strong>WeChat:</strong> zhidasuc</li>
                <li><strong>Instagram:</strong> @mystic_mirror_vip</li>
            </ul>
        </div>

        <div class="success">
            <h2>🚀 Next Steps</h2>
            <ol>
                <li>Configure the webhook in Creem.io</li>
                <li>Test the payment flow</li>
                <li>Deploy to production</li>
                <li>Monitor webhook events</li>
            </ol>
        </div>

        <div class="endpoint-info">
            <h2>🔍 Testing</h2>
            <p>To test the webhook:</p>
            <ol>
                <li>Create a test payment in Creem.io</li>
                <li>Check the browser console for webhook events</li>
                <li>Verify user subscription status updates</li>
                <li>Test the usage limits</li>
            </ol>
        </div>
    </div>

    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;

            navigator.clipboard.writeText(text).then(function() {
                alert('Copied to clipboard!');
            }, function(err) {
                console.error('Could not copy text: ', err);
            });
        }

        // Update the webhook URL with current domain
        document.addEventListener('DOMContentLoaded', function() {
            const webhookUrlElement = document.getElementById('webhook-url');
            const currentDomain = window.location.origin;
            webhookUrlElement.textContent = currentDomain + '/api/webhooks/creem';
        });
    </script>
</body>
</html>
