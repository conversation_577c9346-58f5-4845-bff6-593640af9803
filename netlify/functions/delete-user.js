// Netlify function to completely delete a user account
// This function has service role permissions to delete auth.users

const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = process.env.SUPABASE_URL || 'https://elwmthdxnsyzfvlunvmo.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.iYxvRWGePb0h-hIE33T9bTmRFG-BGheUyc8SOi_rLUo';

// Check if service key is available
if (!SUPABASE_SERVICE_KEY) {
  console.error('❌ SUPABASE_SERVICE_KEY environment variable is not set');
}

// Create Supabase client with service role key
const supabase = SUPABASE_SERVICE_KEY ? createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY) : null;

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  // Handle GET requests for testing environment variables
  if (event.httpMethod === 'GET') {
    console.log('🧪 GET request to delete-user function');
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Delete user function is available and working!',
        hasSupabaseUrl: !!SUPABASE_URL,
        hasServiceKey: !!SUPABASE_SERVICE_KEY,
        supabaseUrl: SUPABASE_URL,
        serviceKeyLength: SUPABASE_SERVICE_KEY ? SUPABASE_SERVICE_KEY.length : 0,
        environment: process.env.NODE_ENV || 'unknown',
        timestamp: new Date().toISOString(),
        functionPath: event.path,
        method: event.httpMethod
      })
    };
  }

  // Only allow POST requests for actual deletion
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // Check if Supabase client is available
    if (!supabase) {
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          error: 'Service configuration error',
          message: 'Supabase service key not configured'
        })
      };
    }

    // Parse request body
    const { user_id, auth_token } = JSON.parse(event.body || '{}');

    if (!user_id || !auth_token) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'user_id and auth_token are required' })
      };
    }

    console.log('🗑️ Starting complete user deletion for:', user_id);

    // Verify the user is authenticated by checking their token
    const { data: { user }, error: authError } = await supabase.auth.getUser(auth_token);

    if (authError || !user || user.id !== user_id) {
      console.error('❌ Authentication failed:', authError);
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Step 1: Delete all user data manually (no need for SQL function)
    console.log('🗑️ Deleting user data manually...');

    let deletionResults = {};

    console.log('🗑️ Manual deletion - reading_feedback...');
    const feedbackResult = await supabase.from('reading_feedback').delete().eq('user_id', user_id);
    deletionResults.reading_feedback = feedbackResult.count || 0;

    console.log('🗑️ Manual deletion - reading_history...');
    const historyResult = await supabase.from('reading_history').delete().eq('user_id', user_id);
    deletionResults.reading_history = historyResult.count || 0;

    console.log('🗑️ Manual deletion - tarot_readings...');
    const tarotResult = await supabase.from('tarot_readings').delete().eq('user_id', user_id);
    deletionResults.tarot_readings = tarotResult.count || 0;

    console.log('🗑️ Manual deletion - payment_history...');
    const paymentResult = await supabase.from('payment_history').delete().eq('user_id', user_id);
    deletionResults.payment_history = paymentResult.count || 0;

    console.log('🗑️ Manual deletion - user_subscriptions...');
    const subscriptionResult = await supabase.from('user_subscriptions').delete().eq('user_id', user_id);
    deletionResults.user_subscriptions = subscriptionResult.count || 0;

    console.log('🗑️ Manual deletion - profiles...');
    const profileResult = await supabase.from('profiles').delete().eq('user_id', user_id);
    deletionResults.profiles = profileResult.count || 0;

    console.log('📊 Manual deletion results:', deletionResults);

    // Step 2: Delete the auth user (this requires service role)
    console.log('🗑️ Deleting auth user...');
    const { error: deleteError } = await supabase.auth.admin.deleteUser(user_id);

    if (deleteError) {
      console.error('❌ Failed to delete auth user:', deleteError);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          error: 'Failed to delete user account',
          details: deleteError.message
        })
      };
    }

    console.log('✅ User completely deleted:', user_id);

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        message: 'User account completely deleted',
        deletionResults,
        user_id,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('❌ Error in delete-user function:', error);

    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Internal server error',
        message: error.message
      })
    };
  }
};
