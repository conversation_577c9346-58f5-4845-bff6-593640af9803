// Netlify function to generate Customer Portal URL
// This avoids CORS issues by calling Creem API from server-side

const CREEM_API_KEY = process.env.CREEM_API_KEY || 'creem_2gPDtvmta7jlXaykq0H9Is';

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // Parse request body
    const { customer_id } = JSON.parse(event.body || '{}');

    if (!customer_id) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'customer_id is required' })
      };
    }

    console.log('🔗 Generating Customer Portal URL for customer:', customer_id);

    // Call Creem API
    const response = await fetch('https://api.creem.io/v1/customers/billing', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': CREEM_API_KEY
      },
      body: JSON.stringify({
        customer_id: customer_id
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Creem API error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });

      return {
        statusCode: response.status,
        headers,
        body: JSON.stringify({
          error: `Creem API error: ${response.statusText}`,
          details: errorText
        })
      };
    }

    const data = await response.json();
    console.log('✅ Customer Portal URL generated successfully');

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        customer_portal_link: data.customer_portal_link
      })
    };

  } catch (error) {
    console.error('❌ Error in customer-portal function:', error);

    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Internal server error',
        message: error.message
      })
    };
  }
};
