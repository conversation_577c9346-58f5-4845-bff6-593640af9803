// Netlify Function to get Creem.io Customer Portal URL
// This allows users to manage their subscriptions through Creem's portal

// Use global fetch (available in Node.js 18+)
const fetch = globalThis.fetch;

const CREEM_API_KEY = "creem_2gPDtvmta7jlXaykq0H9Is";
const CREEM_API_URL = "https://api.creem.io/v1";

exports.handler = async (event, context) => {
  console.log('🚀 Customer Portal URL function called');
  console.log('Method:', event.httpMethod);
  console.log('Body:', event.body);
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  };

  // Handle preflight request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  // Handle GET requests for testing
  if (event.httpMethod === 'GET') {
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Customer Portal URL function is working',
        timestamp: new Date().toISOString()
      }),
    };
  }

  // Only allow POST requests for getting portal URL
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({
        success: false,
        error: 'Method not allowed. Use POST to get customer portal URL or GET to test.'
      }),
    };
  }

  try {
    // Parse request body
    let customerId;
    try {
      const body = JSON.parse(event.body || '{}');
      customerId = body.customerId;
    } catch (parseError) {
      console.error('❌ Failed to parse request body:', parseError);
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({
          success: false,
          error: 'Invalid JSON in request body'
        }),
      };
    }

    if (!customerId) {
      console.error('❌ No customer ID provided');
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({
          success: false,
          error: 'Customer ID is required'
        }),
      };
    }

    console.log('🔄 Getting Creem Customer Portal URL for:', customerId);

    // Call Creem.io API to get Customer Portal URL
    const response = await fetch(`${CREEM_API_URL}/customers/billing`, {
      method: 'POST',
      headers: {
        'x-api-key': CREEM_API_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        customer_id: customerId
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('❌ Creem API error:', response.status, errorData);

      return {
        statusCode: response.status,
        headers,
        body: JSON.stringify({
          success: false,
          error: `Creem API error: ${response.status} ${errorData.message || 'Unknown error'}`
        }),
      };
    }

    const data = await response.json();
    console.log('✅ Creem Customer Portal URL retrieved successfully:', data);

    // 根据Creem.io文档，响应应该包含customer_portal_link字段
    const portalUrl = data.customer_portal_link || data.url || data.redirect_url;

    if (!portalUrl) {
      console.error('❌ No portal URL found in Creem response:', data);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          success: false,
          error: 'No portal URL returned from payment provider'
        }),
      };
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        portalUrl: portalUrl
      }),
    };

  } catch (error) {
    console.error('❌ Error in cancel-subscription function:', error);

    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message || 'Internal server error'
      }),
    };
  }
};
