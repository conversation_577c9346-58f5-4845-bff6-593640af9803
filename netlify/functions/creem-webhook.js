// Netlify Function for handling Creem.io webhooks
// This function will be deployed as a serverless function

const { createClient } = require('@supabase/supabase-js');

// Environment variables (set these in Netlify dashboard)
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY;
const CREEM_API_KEY = process.env.CREEM_API_KEY || 'creem_2gPDtvmta7jlXaykq0H9Is';

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Main webhook handler
exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, x-creem-signature',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // Parse the webhook payload
    const webhookEvent = JSON.parse(event.body);

    console.log('Received Creem webhook event:', webhookEvent.type);
    console.log('Event data:', webhookEvent.data);

    // Verify webhook signature (optional but recommended)
    const signature = event.headers['x-creem-signature'];
    if (!verifySignature(event.body, signature, CREEM_API_KEY)) {
      console.error('Invalid webhook signature');
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid signature' })
      };
    }

    // Process the webhook event
    let result;
    switch (webhookEvent.type) {
      case 'checkout.completed':
      case 'order.completed':
      case 'subscription.created':
        result = await handlePaymentSuccess(webhookEvent.data);
        break;

      case 'subscription.cancelled':
      case 'subscription.expired':
        result = await handleSubscriptionCancelled(webhookEvent.data);
        break;

      case 'checkout.failed':
      case 'order.failed':
        result = await handlePaymentFailed(webhookEvent.data);
        break;

      default:
        console.log('Unhandled event type:', webhookEvent.type);
        result = { success: true, message: 'Event logged' };
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify(result)
    };

  } catch (error) {
    console.error('Webhook processing error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }
};

// Handle payment success events
async function handlePaymentSuccess(eventData) {
  try {
    const { object } = eventData;
    const userId = object.request_id || object.metadata?.userId;

    if (!userId) {
      throw new Error('No user ID found in payment event');
    }

    console.log('Processing payment success for user:', userId);

    // 🔧 新逻辑：Creem付费成功后给用户一次性月度订阅（1个月）
    const subscriptionStart = new Date();
    const subscriptionEnd = new Date();
    subscriptionEnd.setMonth(subscriptionEnd.getMonth() + 1); // 1个月一次性订阅

    // Update user subscription in Supabase
    const { data, error } = await supabase
      .from('user_subscriptions')
      .upsert({
        user_id: userId,
        subscription_type: 'monthly', // 🔧 直接给月度订阅
        status: 'active',
        subscription_id: object.subscription?.id || object.id,
        customer_id: object.customer?.id || object.customer_id, // 保存customer_id用于Customer Portal
        subscription_start_date: subscriptionStart.toISOString(),
        subscription_end_date: subscriptionEnd.toISOString(), // 1个月后过期
        current_period_start: object.subscription?.current_period_start,
        current_period_end: object.subscription?.current_period_end,
        daily_questions_used: 0,
        daily_questions_limit: 20, // 月度会员每天20次
        // 🔧 重要：清空试用期字段，因为这是付费订阅
        trial_start_date: null,
        trial_end_date: null,
        trial_used: true, // 标记已使用过试用期（如果有的话）
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });

    if (error) {
      throw new Error(`Failed to update subscription: ${error.message}`);
    }

    // Record payment history
    if (object.order) {
      await recordPaymentHistory(userId, object.order);
    }

    console.log('Subscription activated successfully for user:', userId);
    return { success: true, message: 'Subscription activated' };

  } catch (error) {
    console.error('Error handling payment success:', error);
    return { success: false, error: error.message };
  }
}

// Handle subscription cancellation
async function handleSubscriptionCancelled(eventData) {
  try {
    const { object } = eventData;
    const userId = object.metadata?.userId || object.request_id;

    if (!userId) {
      throw new Error('No user ID found in cancellation event');
    }

    console.log('Processing subscription cancellation for user:', userId);

    // Update subscription status to cancelled
    const { data, error } = await supabase
      .from('user_subscriptions')
      .update({
        status: 'cancelled',
        subscription_end_date: object.current_period_end || new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Failed to cancel subscription: ${error.message}`);
    }

    console.log('Subscription cancelled successfully for user:', userId);
    return { success: true, message: 'Subscription cancelled' };

  } catch (error) {
    console.error('Error handling subscription cancellation:', error);
    return { success: false, error: error.message };
  }
}

// Handle payment failures
async function handlePaymentFailed(eventData) {
  try {
    const { object } = eventData;
    console.log('Payment failed:', object);

    // Log the failure (could also notify the user)
    return { success: true, message: 'Payment failure logged' };

  } catch (error) {
    console.error('Error handling payment failure:', error);
    return { success: false, error: error.message };
  }
}

// Record payment history
async function recordPaymentHistory(userId, orderData) {
  try {
    const { data, error } = await supabase
      .from('payment_history')
      .insert({
        user_id: userId,
        order_id: orderData.id,
        amount: orderData.amount,
        currency: orderData.currency,
        status: 'completed',
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Failed to record payment history:', error);
    } else {
      console.log('Payment history recorded successfully');
    }
  } catch (error) {
    console.error('Error recording payment history:', error);
  }
}

// Verify webhook signature (simplified version)
function verifySignature(payload, signature, apiKey) {
  // In a real implementation, you would verify the HMAC signature
  // For now, we'll just check if signature exists
  return signature && signature.length > 0;
}

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    handler: exports.handler,
    handlePaymentSuccess,
    handleSubscriptionCancelled,
    handlePaymentFailed
  };
}
