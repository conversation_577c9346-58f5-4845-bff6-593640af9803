<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Mystic Mirror</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e1b4b 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            border: 1px solid rgba(139, 92, 246, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            font-size: 28px;
            background: linear-gradient(45deg, #8b5cf6, #a855f7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .logo p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .form-section h2 {
            font-size: 24px;
            margin-bottom: 10px;
            text-align: center;
        }

        .form-section p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: rgba(139, 92, 246, 0.6);
            background: rgba(255, 255, 255, 0.15);
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .btn {
            width: 100%;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #8b5cf6, #a855f7);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(45deg, #7c3aed, #9333ea);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: rgba(108, 117, 125, 0.8);
            color: white;
            border: 1px solid rgba(108, 117, 125, 0.5);
        }

        .btn-secondary:hover {
            background: rgba(90, 98, 104, 0.9);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }

        .message.success {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #22c55e;
        }

        .message.error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: rgba(139, 92, 246, 0.8);
            text-decoration: none;
            font-size: 14px;
        }

        .back-link a:hover {
            color: rgba(139, 92, 246, 1);
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>🔮 Mystic Mirror</h1>
            <p>Reset Your Password</p>
        </div>

        <!-- 重设密码表单 -->
        <div id="resetForm" class="form-section">
            <h2>Reset Password</h2>
            <p>Enter your new password below. Make sure it's at least 6 characters long.</p>
            
            <div id="messageDiv" class="message hidden"></div>
            
            <form id="resetPasswordForm">
                <div class="form-group">
                    <label for="newPassword">New Password</label>
                    <input type="password" id="newPassword" placeholder="Enter new password (min 6 chars)" required minlength="6">
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">Confirm Password</label>
                    <input type="password" id="confirmPassword" placeholder="Confirm new password" required>
                </div>
                
                <button type="submit" class="btn btn-primary" id="resetBtn">
                    <span id="resetBtnText">Reset Password</span>
                    <div id="resetBtnLoading" class="loading hidden">
                        <div class="spinner"></div>
                        <span>Resetting...</span>
                    </div>
                </button>
            </form>
        </div>

        <!-- 成功页面 -->
        <div id="successSection" class="form-section hidden">
            <h2>✅ Password Reset Successful</h2>
            <p>Your password has been successfully reset. You can now login with your new password.</p>
            
            <button type="button" class="btn btn-primary" onclick="window.close()">
                Close Window
            </button>
        </div>

        <div class="back-link">
            <a href="https://mystic-card-mirror-vip.lovable.app/" target="_blank">← Back to Mystic Mirror</a>
        </div>
    </div>

    <script>
        // 从URL获取参数
        const urlParams = new URLSearchParams(window.location.search);
        const accessToken = urlParams.get('access_token');
        const refreshToken = urlParams.get('refresh_token');
        const type = urlParams.get('type');

        // Supabase配置
        const SUPABASE_URL = 'https://elwmthdxnsyzfvlunvmo.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsd210aGR4bnN5emZ2bHVudm1vIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NjI4NzQsImV4cCI6MjA1MDUzODg3NH0.YQJhOGJhZGJhZGJhZGJhZGJhZGJhZGJhZGJhZGJhZGJh';

        // DOM元素
        const elements = {
            resetForm: document.getElementById('resetForm'),
            successSection: document.getElementById('successSection'),
            messageDiv: document.getElementById('messageDiv'),
            resetPasswordForm: document.getElementById('resetPasswordForm'),
            newPassword: document.getElementById('newPassword'),
            confirmPassword: document.getElementById('confirmPassword'),
            resetBtn: document.getElementById('resetBtn'),
            resetBtnText: document.getElementById('resetBtnText'),
            resetBtnLoading: document.getElementById('resetBtnLoading')
        };

        // 检查是否有有效的重设令牌
        if (!accessToken || type !== 'recovery') {
            showMessage('Invalid or expired reset link. Please request a new password reset.', 'error');
            elements.resetPasswordForm.style.display = 'none';
        }

        // 处理表单提交
        elements.resetPasswordForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const newPassword = elements.newPassword.value;
            const confirmPassword = elements.confirmPassword.value;
            
            // 验证密码
            if (newPassword.length < 6) {
                showMessage('Password must be at least 6 characters long.', 'error');
                return;
            }
            
            if (newPassword !== confirmPassword) {
                showMessage('Passwords do not match.', 'error');
                return;
            }
            
            // 显示加载状态
            elements.resetBtnText.classList.add('hidden');
            elements.resetBtnLoading.classList.remove('hidden');
            elements.resetBtn.disabled = true;
            
            try {
                // 调用Supabase API重设密码
                const response = await fetch(`${SUPABASE_URL}/auth/v1/user`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${accessToken}`,
                        'apikey': SUPABASE_ANON_KEY
                    },
                    body: JSON.stringify({
                        password: newPassword
                    })
                });
                
                if (response.ok) {
                    // 成功重设密码
                    elements.resetForm.classList.add('hidden');
                    elements.successSection.classList.remove('hidden');
                } else {
                    const errorData = await response.json();
                    showMessage(errorData.message || 'Failed to reset password. Please try again.', 'error');
                }
                
            } catch (error) {
                console.error('Reset password error:', error);
                showMessage('An error occurred. Please try again later.', 'error');
            } finally {
                // 恢复按钮状态
                elements.resetBtnText.classList.remove('hidden');
                elements.resetBtnLoading.classList.add('hidden');
                elements.resetBtn.disabled = false;
            }
        });

        // 显示消息
        function showMessage(message, type) {
            elements.messageDiv.textContent = message;
            elements.messageDiv.className = `message ${type}`;
            elements.messageDiv.classList.remove('hidden');
        }
    </script>
</body>
</html>
