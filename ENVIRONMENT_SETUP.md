# 🔐 环境变量配置指南

## 概述
为了安全起见，API密钥现在存储在环境变量中，而不是直接写在代码里。

## 🚀 Lovable平台配置

### 在Lovable中设置环境变量：

1. **登录Lovable Dashboard**
   - 访问 [lovable.dev](https://lovable.dev)
   - 找到项目：`mystic-card-mirror-vip`

2. **设置环境变量**
   - 进入项目设置
   - 找到 "Environment Variables" 或 "Secrets" 部分
   - 添加以下变量：

```
VITE_DEEPSEEK_API_KEY=***********************************
```

3. **重新部署**
   - 保存环境变量后
   - 触发重新部署以应用新配置

## 🔧 本地开发配置

### .env.local 文件：
```bash
# DeepSeek API Configuration - 安全存储
VITE_DEEPSEEK_API_KEY=***********************************
```

**注意**：`.env.local` 文件已在 `.gitignore` 中，不会被提交到Git。

## 🔍 验证配置

### 检查环境变量是否正确设置：

1. **查看浏览器控制台**
   - 应该看到：`🔐 Secure API Configuration:`
   - `hasApiKey: true`
   - `keyLength: 51`
   - `keyPrefix: sk-f1326***`

2. **如果配置错误**
   - 会显示：`❌ DEEPSEEK_API_KEY not found in environment variables!`
   - 需要检查环境变量设置

## 🛡️ 安全优势

### 之前的问题：
- ❌ API密钥直接写在代码中
- ❌ 在日志中完整显示密钥
- ❌ 密钥暴露在GitHub仓库中

### 现在的安全措施：
- ✅ API密钥存储在环境变量中
- ✅ 日志只显示密钥前缀（sk-f1326***）
- ✅ 代码中不包含敏感信息
- ✅ 支持不同环境使用不同密钥

## 📋 部署检查清单

- [ ] 在Lovable中设置 `VITE_DEEPSEEK_API_KEY`
- [ ] 触发重新部署
- [ ] 检查浏览器控制台确认配置正确
- [ ] 测试API调用是否正常工作
- [ ] 确认日志不再显示完整密钥

## 🚨 故障排除

### 如果API调用失败：

1. **检查环境变量**
   ```javascript
   console.log('API Key exists:', !!import.meta.env.VITE_DEEPSEEK_API_KEY);
   ```

2. **检查密钥格式**
   - 应该以 `sk-` 开头
   - 长度应该是51个字符

3. **重新部署**
   - 环境变量更改后需要重新部署才能生效

## 🔗 相关链接

- **Lovable Dashboard**: https://lovable.dev
- **项目URL**: https://mystic-card-mirror-vip.lovable.app
- **DeepSeek API文档**: https://api.deepseek.com/docs
