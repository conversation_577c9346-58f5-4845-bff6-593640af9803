-- 修复新用户试用期问题
-- 确保所有新注册用户都能获得3天试用期

-- 1. 首先检查当前的触发器函数
SELECT 
    routine_name, 
    routine_definition 
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user' 
    AND routine_schema = 'public';

-- 2. 更新触发器函数，确保新用户获得3天试用期
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    trial_end_date TIMESTAMPTZ;
BEGIN
    -- 计算3天试用期结束时间
    trial_end_date := NOW() + INTERVAL '3 days';
    
    -- 为新用户创建3天试用期订阅
    INSERT INTO public.user_subscriptions (
        user_id,
        subscription_type,
        status,
        daily_questions_limit,
        daily_questions_used,
        trial_start_date,
        trial_end_date,
        trial_used,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        'free_trial',
        'active',
        20,  -- 试用期每天20次
        0,
        NOW(),
        trial_end_date,
        true,
        NOW(),
        NOW()
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. 确保触发器存在
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 4. 修复现有的免费用户，给他们3天试用期
-- 查找所有免费用户或没有试用期的用户
UPDATE public.user_subscriptions 
SET 
    subscription_type = 'free_trial',
    status = 'active',
    daily_questions_limit = 20,
    trial_start_date = NOW(),
    trial_end_date = NOW() + INTERVAL '3 days',
    trial_used = true,
    updated_at = NOW()
WHERE 
    (subscription_type = 'free' OR subscription_type IS NULL)
    AND status = 'active' 
    AND (trial_used IS NOT TRUE OR trial_used IS NULL)
    AND (trial_end_date IS NULL OR trial_end_date < NOW());

-- 5. 特别修复 <EMAIL> 用户
-- 首先查找这个用户的ID
DO $$
DECLARE
    user_uuid UUID;
BEGIN
    -- 查找用户ID
    SELECT id INTO user_uuid 
    FROM auth.users 
    WHERE email = '<EMAIL>';
    
    IF user_uuid IS NOT NULL THEN
        -- 更新或插入订阅记录
        INSERT INTO public.user_subscriptions (
            user_id,
            subscription_type,
            status,
            daily_questions_limit,
            daily_questions_used,
            trial_start_date,
            trial_end_date,
            trial_used,
            created_at,
            updated_at
        ) VALUES (
            user_uuid,
            'free_trial',
            'active',
            20,
            0,
            NOW(),
            NOW() + INTERVAL '3 days',
            true,
            NOW(),
            NOW()
        )
        ON CONFLICT (user_id) 
        DO UPDATE SET
            subscription_type = 'free_trial',
            status = 'active',
            daily_questions_limit = 20,
            daily_questions_used = 0,
            trial_start_date = NOW(),
            trial_end_date = NOW() + INTERVAL '3 days',
            trial_used = true,
            updated_at = NOW();
            
        RAISE NOTICE 'Updated <NAME_EMAIL> (ID: %)', user_uuid;
    ELSE
        RAISE NOTICE 'User <EMAIL> not found';
    END IF;
END $$;

-- 6. 验证修复结果
SELECT 
    u.email,
    s.subscription_type,
    s.status,
    s.daily_questions_limit,
    s.trial_start_date,
    s.trial_end_date,
    s.trial_used,
    CASE 
        WHEN s.trial_end_date > NOW() THEN 'Active Trial'
        WHEN s.subscription_type = 'monthly' THEN 'Paid Subscription'
        ELSE 'Free User'
    END as current_status
FROM auth.users u
LEFT JOIN public.user_subscriptions s ON u.id = s.user_id
WHERE u.email IN ('<EMAIL>', '<EMAIL>')
ORDER BY u.email;
