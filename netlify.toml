[build]
  publish = "dist"
  command = "npm run build"

[functions]
  directory = "netlify/functions"

[[redirects]]
  from = "/api/webhooks/creem"
  to = "/.netlify/functions/creem-webhook"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[build.environment]
  NODE_VERSION = "18"

# Environment variables needed for the webhook function
# Set these in Netlify dashboard:
# SUPABASE_URL = https://elwmthdxnsyzfvlunvmo.supabase.co
# SUPABASE_SERVICE_KEY = your_supabase_service_role_key (需要从Supabase获取)
# CREEM_API_KEY = creem_2gPDtvmta7jlXaykq0H9Is
