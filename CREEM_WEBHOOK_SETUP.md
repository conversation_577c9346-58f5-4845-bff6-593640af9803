# 🚀 Creem.io Webhook 快速配置

## 📋 立即复制使用的配置信息

### **Webhook Name:**
```
Mystic Card Mirror - Payment Events
```

### **Webhook URL:**
```
https://mystic-card-mirror-vip.lovable.app/api/webhooks/creem
```

### **HTTP Method:**
```
POST
```

### **Content Type:**
```
application/json
```

## ✅ 需要选择的事件 (全部勾选)

- ✅ `order.created`
- ✅ `order.completed`
- ✅ `order.failed`
- ✅ `subscription.created`
- ✅ `subscription.updated`
- ✅ `subscription.cancelled`
- ✅ `subscription.expired`
- ✅ `customer.created`
- ✅ `customer.updated`

## 🔧 配置步骤

1. **登录 Creem.io 仪表板**
2. **找到 Webhooks 设置**
3. **点击"添加新Webhook"**
4. **复制粘贴上面的信息**
5. **选择所有事件类型**
6. **启用签名验证**
7. **保存配置**

## 🛒 测试产品信息

- **测试产品ID：** `prod_2Ei8CdkAewpPbGZFz09Cgq`
- **测试产品链接：** https://www.creem.io/test/payment/prod_2Ei8CdkAewpPbGZFz09Cgq
- **价格：** $4.99/月
- **描述：** Monthly Premium Subscription

## 🌐 您的网站信息

- **官网：** https://mystic-card-mirror-vip.lovable.app
- **开发环境：** http://localhost:8080
- **Webhook测试页面：** https://mystic-card-mirror-vip.lovable.app/webhook-test.html

## 📞 联系信息

- **邮箱：** <EMAIL>
- **微信：** zhidasuc
- **Instagram：** @mystic_mirror_vip

## 🎯 配置完成后的功能

✅ 自动处理支付成功
✅ 更新用户订阅状态
✅ 记录支付历史
✅ 处理订阅取消
✅ 发送确认通知

---

**重要提醒：** 配置完成后请测试一下Webhook连接，确保事件能正常接收！

🔮 祝您配置顺利！
