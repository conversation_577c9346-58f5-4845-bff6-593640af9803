# 🔗 Creem.io Webhook 配置指南

## 📋 Webhook 配置信息

### Webhook Name (Webhook名称)
```
Mystic Card Mirror - Payment Events
```

**说明：** 这个名称用于在Creem.io平台中识别您的webhook。建议使用描述性的名称，方便管理。

### Webhook URL (Webhook地址)
```
https://mystic-card-mirror-vip.lovable.app/api/webhooks/creem
```

**重要提醒：**
- 开发环境：`http://localhost:8080/api/webhooks/creem`
- 生产环境：`https://mystic-card-mirror-vip.lovable.app/api/webhooks/creem`

## ⚙️ 详细配置步骤

### 1. 登录Creem.io仪表板
- 访问 https://creem.io
- 使用您的账户登录

### 2. 导航到Webhook设置
- 在仪表板中找到 "Webhooks" 或"集成"选项
- 点击"添加新Webhook"或"Create Webhook"

### 3. 填写Webhook信息

#### Webhook Name (必填)
```
Mystic Card Mirror - Payment Events
```

#### Webhook URL (必填)
```
https://mystic-card-mirror-vip.lovable.app/api/webhooks/creem
```

#### HTTP Method (HTTP方法)
```
POST
```

#### Content Type (内容类型)
```
application/json
```

### 4. 选择事件类型

请勾选以下所有事件：

- ✅ `checkout.session.completed` - 支付会话完成
- ✅ `checkout.session.failed` - 支付会话失败
- ✅ `subscription.created` - 订阅创建
- ✅ `subscription.updated` - 订阅更新
- ✅ `subscription.cancelled` - 订阅取消
- ✅ `payment.succeeded` - 支付成功
- ✅ `payment.failed` - 支付失败

### 5. 安全设置

#### 启用签名验证
- ✅ 启用Webhook签名验证
- 保存提供的签名密钥，用于验证请求真实性

#### 重试设置
- 重试次数：3次
- 重试间隔：5秒、30秒、5分钟

## 🔧 测试配置

### 1. 保存Webhook配置
点击"保存"或"Create"按钮

### 2. 测试连接
- 使用Creem.io提供的测试功能
- 发送测试事件到您的Webhook URL
- 检查是否收到测试请求

### 3. 验证响应
确保您的服务器返回：
- HTTP状态码：200
- 响应时间：< 30秒

## 📊 Webhook事件处理

### 支付成功事件示例
```json
{
  "id": "evt_1234567890",
  "type": "checkout.session.completed",
  "data": {
    "object": {
      "id": "cs_1234567890",
      "amount_total": 499,
      "currency": "usd",
      "customer": {
        "id": "cus_1234567890",
        "email": "<EMAIL>"
      },
      "metadata": {
        "user_id": "user_uuid_here",
        "subscription_type": "monthly"
      }
    }
  },
  "created": 1234567890
}
```

### 订阅取消事件示例
```json
{
  "id": "evt_0987654321",
  "type": "subscription.cancelled",
  "data": {
    "object": {
      "id": "sub_0987654321",
      "status": "cancelled",
      "metadata": {
        "user_id": "user_uuid_here"
      }
    }
  },
  "created": 1234567890
}
```

## 🛡️ 安全最佳实践

### 1. 验证Webhook签名
```javascript
const crypto = require('crypto');

function verifyWebhookSignature(payload, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');

  return signature === `sha256=${expectedSignature}`;
}
```

### 2. 处理重复事件
- 使用事件ID去重
- 实现幂等性处理
- 记录已处理的事件

### 3. 错误处理
- 返回适当的HTTP状态码
- 记录错误日志
- 实现重试机制

## 📝 配置检查清单

- [ ] Webhook名称已设置：`Mystic Card Mirror - Payment Events`
- [ ] Webhook URL已配置正确
- [ ] 所有必要事件已选择
- [ ] 签名验证已启用
- [ ] 测试事件发送成功
- [ ] 服务器正确响应测试请求
- [ ] 错误处理逻辑已实现
- [ ] 日志记录已配置

## 🔍 故障排除

### 常见问题

#### 1. Webhook URL无法访问
- 检查域名是否正确
- 确认服务器正在运行
- 验证防火墙设置

#### 2. 事件未收到
- 检查事件类型选择
- 验证URL路径正确性
- 查看Creem.io的重试日志

#### 3. 签名验证失败
- 确认签名密钥正确
- 检查签名算法实现
- 验证请求头格式

### 调试工具

#### 1. 使用ngrok进行本地测试
```bash
# 安装ngrok
npm install -g ngrok

# 启动隧道
ngrok http 8080

# 使用提供的HTTPS URL作为Webhook URL
```

#### 2. 查看Webhook日志
- 在Creem.io仪表板中查看发送日志
- 检查响应状态和错误信息
- 分析重试记录

## 📞 支持联系

如果在配置过程中遇到问题：

- **邮箱：** <EMAIL>
- **微信：** zhidasuc
- **Instagram：** @mystic_mirror_vip

## 🎯 配置完成后

配置成功后，您的系统将能够：
- 自动处理支付成功事件
- 更新用户订阅状态
- 记录支付历史
- 处理订阅取消
- 发送确认通知

祝您配置顺利！🚀
