// 背景脚本 - 处理插件生命周期和通知

// 插件安装时的初始化
chrome.runtime.onInstalled.addListener((details) => {
  console.log('🔮 Mystic Mirror Extension 已安装');
  
  if (details.reason === 'install') {
    // 首次安装
    chrome.storage.local.set({
      language: 'zh',
      notifications: true,
      dailyReminder: true
    });
    
    // 打开欢迎页面
    chrome.tabs.create({
      url: 'https://mystic-card-mirror-vip.lovable.app/?source=extension'
    });
  }
});

// 设置每日提醒闹钟
chrome.runtime.onStartup.addListener(() => {
  setupDailyReminder();
});

// 设置每日提醒
async function setupDailyReminder() {
  try {
    // 清除现有闹钟
    await chrome.alarms.clear('dailyTarotReminder');
    
    // 设置每日上午9点提醒
    const now = new Date();
    const reminderTime = new Date();
    reminderTime.setHours(9, 0, 0, 0);
    
    // 如果今天的提醒时间已过，设置为明天
    if (reminderTime <= now) {
      reminderTime.setDate(reminderTime.getDate() + 1);
    }
    
    chrome.alarms.create('dailyTarotReminder', {
      when: reminderTime.getTime(),
      periodInMinutes: 24 * 60 // 每24小时重复
    });
    
    console.log('✅ 每日提醒已设置:', reminderTime.toLocaleString());
  } catch (error) {
    console.error('设置每日提醒失败:', error);
  }
}

// 处理闹钟事件
chrome.alarms.onAlarm.addListener(async (alarm) => {
  if (alarm.name === 'dailyTarotReminder') {
    await showDailyReminder();
  }
});

// 显示每日提醒通知
async function showDailyReminder() {
  try {
    // 检查用户设置
    const settings = await chrome.storage.local.get(['notifications', 'dailyReminder']);
    if (!settings.notifications || !settings.dailyReminder) {
      return;
    }
    
    // 检查今天是否已经抽过卡
    const today = new Date().toDateString();
    const result = await chrome.storage.local.get(['lastReadingDate']);
    
    if (result.lastReadingDate === today) {
      // 今天已经抽过卡了，不显示提醒
      return;
    }
    
    // 显示通知
    chrome.notifications.create('dailyTarotReminder', {
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: '🔮 神秘镜像 - 每日塔罗',
      message: '新的一天开始了！抽取您的每日运势卡牌，获得今日指引。',
      buttons: [
        { title: '立即抽卡' },
        { title: '稍后提醒' }
      ],
      requireInteraction: true
    });
    
  } catch (error) {
    console.error('显示每日提醒失败:', error);
  }
}

// 处理通知点击事件
chrome.notifications.onClicked.addListener((notificationId) => {
  if (notificationId === 'dailyTarotReminder') {
    // 打开插件弹窗
    chrome.action.openPopup();
    chrome.notifications.clear(notificationId);
  }
});

// 处理通知按钮点击
chrome.notifications.onButtonClicked.addListener((notificationId, buttonIndex) => {
  if (notificationId === 'dailyTarotReminder') {
    if (buttonIndex === 0) {
      // 立即抽卡
      chrome.action.openPopup();
    } else if (buttonIndex === 1) {
      // 稍后提醒 - 1小时后再提醒
      chrome.alarms.create('laterReminder', {
        delayInMinutes: 60
      });
    }
    chrome.notifications.clear(notificationId);
  }
});

// 处理稍后提醒
chrome.alarms.onAlarm.addListener((alarm) => {
  if (alarm.name === 'laterReminder') {
    showDailyReminder();
  }
});

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'openWebsite') {
    chrome.tabs.create({ url: request.url });
    sendResponse({ success: true });
  } else if (request.action === 'updateBadge') {
    // 更新插件图标徽章
    chrome.action.setBadgeText({
      text: request.text || ''
    });
    chrome.action.setBadgeBackgroundColor({
      color: request.color || '#8b5cf6'
    });
    sendResponse({ success: true });
  } else if (request.action === 'setupReminder') {
    setupDailyReminder();
    sendResponse({ success: true });
  }
  
  return true; // 保持消息通道开放
});

// 监听标签页更新，检测官网登录状态
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && 
      tab.url && 
      tab.url.includes('mystic-card-mirror-vip.lovable.app')) {
    
    try {
      // 尝试从官网获取用户登录状态
      const results = await chrome.scripting.executeScript({
        target: { tabId: tabId },
        function: extractUserSession
      });
      
      if (results && results[0] && results[0].result) {
        const userSession = results[0].result;
        if (userSession) {
          // 保存用户会话到插件存储
          await chrome.storage.local.set({ userSession });
          console.log('✅ 用户会话已同步:', userSession.email);
        }
      }
    } catch (error) {
      // 忽略错误，可能是权限问题或页面还未完全加载
      console.log('无法获取用户会话:', error.message);
    }
  }
});

// 从官网页面提取用户会话信息
function extractUserSession() {
  try {
    // 尝试从localStorage获取用户信息
    const authData = localStorage.getItem('supabase.auth.token');
    if (authData) {
      const parsed = JSON.parse(authData);
      if (parsed.user) {
        return {
          id: parsed.user.id,
          email: parsed.user.email,
          subscription_type: 'free' // 默认值，实际需要从API获取
        };
      }
    }
    
    // 尝试其他方式获取用户信息
    const userElement = document.querySelector('[data-user-email]');
    if (userElement) {
      return {
        email: userElement.getAttribute('data-user-email'),
        subscription_type: userElement.getAttribute('data-subscription-type') || 'free'
      };
    }
    
    return null;
  } catch (error) {
    console.error('提取用户会话失败:', error);
    return null;
  }
}

// 初始化背景脚本
console.log('🔮 Mystic Mirror Background Script 已加载');

// 设置初始提醒
setupDailyReminder();
