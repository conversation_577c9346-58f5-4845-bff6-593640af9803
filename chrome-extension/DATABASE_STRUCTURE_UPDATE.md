# 🗄️ Chrome插件数据库结构更新

## 📊 数据库表结构

### `reading_history` 表
```sql
CREATE TABLE reading_history (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    question TEXT,
    cards JSONB,
    ai_reading TEXT,
    spread_type VARCHAR,
    created_at TIMESTAMP WITH TIME ZONE
);
```

### `reading_feedback` 表
```sql
CREATE TABLE reading_feedback (
    id UUID PRIMARY KEY,
    reading_id UUID NOT NULL REFERENCES reading_history(id),
    user_id UUID NOT NULL,
    accuracy_rating INTEGER,
    satisfaction_rating INTEGER,
    usefulness_rating INTEGER,
    feedback_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE
);
```

## 🔧 Chrome插件数据保存更新

### 1. 解读数据保存 (`saveReadingToDatabase`)

#### **更新前的数据结构：**
```javascript
{
  user_id: currentUser.id,
  card_name: cardName,
  card_position: position,
  reading_content: JSON.stringify(aiReading),
  reading_type: 'daily_fortune',
  language: currentLanguage
}
```

#### **更新后的数据结构：**
```javascript
{
  user_id: currentUser.id,
  question: currentLanguage === 'zh' ? '今日运势' : 'Daily Fortune',
  cards: JSON.stringify([{
    name: cardName,
    name_en: card.nameEn,
    name_zh: card.name,
    position: position,
    meaning: card.meaning,
    image: card.image
  }]),
  ai_reading: JSON.stringify(aiReading),
  spread_type: 'daily_fortune',
  created_at: new Date().toISOString()
}
```

### 2. 评分数据保存 (`saveRatingToDatabase`)

#### **更新前的数据结构：**
```javascript
{
  reading_id: readingId,
  user_id: currentUser.id,
  rating: rating,
  created_at: new Date().toISOString()
}
```

#### **更新后的数据结构：**
```javascript
{
  reading_id: readingId,
  user_id: currentUser.id,
  accuracy_rating: rating,      // 准确度评分
  satisfaction_rating: rating,  // 满意度评分
  usefulness_rating: rating,    // 实用性评分
  feedback_text: null,          // 反馈文本
  created_at: new Date().toISOString()
}
```

## 📈 数据结构优势

### `reading_history` 表优势：
1. **标准化结构** - 与网站使用相同的表结构
2. **丰富的卡牌信息** - 保存完整的卡牌数据（双语名称、含义、图片）
3. **灵活的问题字段** - 支持不同类型的塔罗解读
4. **JSONB格式** - 高效存储和查询卡牌数组数据
5. **牌阵类型** - 支持扩展到其他类型的塔罗牌阵

### `reading_feedback` 表优势：
1. **多维度评分** - 准确度、满意度、实用性三个维度
2. **文本反馈** - 支持用户提供详细反馈
3. **数据分析** - 便于分析用户满意度和改进产品

## 🔄 数据兼容性

### 网站端兼容：
- ✅ **完全兼容** - 插件数据可在网站History页面正常显示
- ✅ **评分同步** - 插件评分可在网站查看和管理
- ✅ **搜索过滤** - 支持按牌阵类型、日期等过滤

### 插件端兼容：
- ✅ **向后兼容** - 现有本地存储数据不受影响
- ✅ **渐进增强** - 新功能不影响现有用户体验
- ✅ **错误处理** - 数据库保存失败时优雅降级

## 🎯 用户体验改进

### 数据持久化：
- **云端同步** - 插件数据保存到云端，不会丢失
- **跨设备访问** - 可在网站查看插件生成的历史记录
- **数据备份** - 自动备份，无需担心数据丢失

### 功能增强：
- **详细评分** - 多维度评分提供更准确的反馈
- **历史分析** - 可分析个人塔罗解读历史趋势
- **个性化推荐** - 基于历史数据提供个性化建议

## 🚀 实施状态

### ✅ 已完成：
- [x] 更新 `saveReadingToDatabase` 函数
- [x] 更新 `saveRatingToDatabase` 函数
- [x] 添加详细的调试日志
- [x] 保持向后兼容性
- [x] 错误处理和优雅降级

### 📋 测试检查清单：
- [ ] 插件抽卡后数据正确保存到 `reading_history`
- [ ] 网站History页面显示插件生成的记录
- [ ] 评分功能正常工作并保存到 `reading_feedback`
- [ ] 卡牌信息完整（双语名称、位置、含义）
- [ ] AI解读内容正确保存和显示
- [ ] 错误情况下的优雅降级

## 🔮 预期效果

### 数据质量：
- **结构化存储** - 卡牌数据结构化，便于查询和分析
- **完整信息** - 保存完整的解读上下文
- **多语言支持** - 支持中英文双语数据

### 用户价值：
- **历史追踪** - 可追踪个人塔罗解读历史
- **趋势分析** - 分析运势变化趋势
- **反馈改进** - 基于用户评分持续改进AI解读质量

---

**所有数据库结构更新已完成，插件现在与网站使用统一的数据结构！** 🎉✨🗄️
