<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mystic Mirror - 每日塔罗运势</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <!-- 头部 -->
    <header class="header">
      <div class="logo">
        <img src="icons/icon32.png" alt="Mystic Mirror" class="logo-icon">
        <h1 id="logoTitle">Mystic Mirror</h1>
      </div>
      <div class="header-actions">
        <button id="languageBtn" class="btn-icon" title="切换语言">🌐</button>
        <button id="openWebsiteBtn" class="btn-icon" title="打开官网">🔗</button>
      </div>
    </header>

    <!-- 用户状态区域 -->
    <div id="userSection" class="user-section">
      <!-- 未登录状态 -->
      <div id="loginPrompt" class="login-prompt">
        <p id="loginText" class="login-text">登录获取每日塔罗运势</p>
        <div class="login-buttons">
          <button id="loginBtn" class="btn btn-primary">登录</button>
          <button id="registerBtn" class="btn btn-secondary">注册</button>
        </div>
      </div>

      <!-- 登录表单 -->
      <div id="loginForm" class="auth-form hidden">
        <h3 id="loginFormTitle">用户登录</h3>
        <form id="loginFormElement">
          <div class="form-group">
            <input type="email" id="loginEmail" placeholder="邮箱地址" required>
          </div>
          <div class="form-group">
            <input type="password" id="loginPassword" placeholder="密码" required>
          </div>
          <div class="form-actions">
            <button type="submit" class="btn btn-primary" id="loginSubmitBtn">
              <span id="loginSubmitText">登录</span>
              <div id="loginSubmitLoading" class="loading hidden">
                <div class="spinner"></div>
                <span>登录中...</span>
              </div>
            </button>
            <button type="button" id="backToPromptBtn" class="btn btn-secondary">返回</button>
          </div>
        </form>
        <p class="form-switch">
          <span id="noAccountText">还没有账户？</span>
          <button type="button" id="switchToRegisterBtn" class="btn-text">立即注册</button>
        </p>
      </div>

      <!-- 注册表单 -->
      <div id="registerForm" class="auth-form hidden">
        <h3 id="registerFormTitle">用户注册</h3>
        <form id="registerFormElement">
          <div class="form-group">
            <input type="email" id="registerEmail" placeholder="邮箱地址" required>
          </div>
          <div class="form-group">
            <input type="password" id="registerPassword" placeholder="密码（至少6位）" required minlength="6">
          </div>
          <div class="form-group">
            <input type="password" id="confirmPassword" placeholder="确认密码" required>
          </div>
          <div class="form-actions">
            <button type="submit" class="btn btn-primary" id="registerSubmitBtn">
              <span id="registerSubmitText">注册</span>
              <div id="registerSubmitLoading" class="loading hidden">
                <div class="spinner"></div>
                <span>注册中...</span>
              </div>
            </button>
            <button type="button" id="backToPromptBtn2" class="btn btn-secondary">返回</button>
          </div>
        </form>
        <p class="form-switch">
          <span id="hasAccountText">已有账户？</span>
          <button type="button" id="switchToLoginBtn" class="btn-text">立即登录</button>
        </p>
      </div>

      <!-- 已登录状态 -->
      <div id="userInfo" class="user-info hidden">
        <div class="user-details">
          <span id="userEmail" class="user-email"></span>
          <span id="userPlan" class="user-plan"></span>
        </div>
        <button id="logoutBtn" class="btn-text">退出</button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <main id="mainContent" class="main-content">
      <!-- 每日运势卡片 -->
      <div id="dailyFortune" class="daily-fortune">
        <div class="fortune-header">
          <h2 id="fortuneTitle">今日运势</h2>
          <span id="fortuneDate" class="fortune-date"></span>
        </div>

        <!-- 塔罗牌显示 -->
        <div id="tarotCard" class="tarot-card">
          <div id="cardPlaceholder" class="card-placeholder">
            <div class="card-back">🔮</div>
            <p>点击抽取今日运势卡牌</p>
          </div>

          <div id="cardResult" class="card-result hidden">
            <div class="card-image">
              <img id="cardImg" src="" alt="塔罗牌">
              <div class="card-name" id="cardName"></div>
              <div class="card-position" id="cardPosition"></div>
            </div>
          </div>
        </div>

        <!-- 抽卡按钮 -->
        <button id="drawCardBtn" class="btn btn-primary btn-draw">
          <span id="drawBtnText">抽取今日运势</span>
          <div id="drawBtnLoading" class="loading hidden">
            <div class="spinner"></div>
            <span>解读中...</span>
          </div>
        </button>
      </div>

      <!-- AI解读区域 -->
      <div id="aiReading" class="ai-reading hidden">
        <h3>🔮 AI塔罗解读</h3>
        <div id="readingContent" class="reading-content">
          <div class="reading-section">
            <h4>📊 运势分析</h4>
            <p id="fortuneAnalysis" class="reading-text"></p>
          </div>

          <div class="reading-section">
            <h4>🎨 今日建议</h4>
            <div id="dailyAdvice" class="advice-grid">
              <div class="advice-item">
                <span class="advice-icon">👕</span>
                <div class="advice-content">
                  <strong>幸运颜色</strong>
                  <p id="luckyColor"></p>
                </div>
              </div>
              <div class="advice-item">
                <span class="advice-icon">📍</span>
                <div class="advice-content">
                  <strong>适宜地点</strong>
                  <p id="luckyLocation"></p>
                </div>
              </div>
              <div class="advice-item">
                <span class="advice-icon">⏰</span>
                <div class="advice-content">
                  <strong>最佳时间</strong>
                  <p id="luckyTime"></p>
                </div>
              </div>
              <div class="advice-item">
                <span class="advice-icon">✨</span>
                <div class="advice-content">
                  <strong>行动指导</strong>
                  <p id="actionGuide"></p>
                </div>
              </div>
            </div>
          </div>

          <div class="reading-section">
            <h4>🙏 正念提醒</h4>
            <div id="mindfulnessReminder" class="mindfulness">
              <p id="dailyGoodDeed" class="good-deed"></p>
              <p id="positiveThought" class="positive-thought"></p>
            </div>
          </div>
        </div>
      </div>

      <!-- 升级提示 -->
      <div id="upgradePrompt" class="upgrade-prompt hidden">
        <div class="upgrade-content">
          <h3>🌟 解锁更多功能</h3>
          <p>升级到高级版获得更详细的解读和建议</p>
          <button id="upgradeBtn" class="btn btn-premium">立即升级</button>
        </div>
      </div>
    </main>

    <!-- 底部 -->
    <footer class="footer">
      <div class="footer-links">
        <button id="historyBtn" class="btn-text">History</button>
        <button id="settingsBtn" class="btn-text">Settings</button>
        <button id="helpBtn" class="btn-text">Help</button>
      </div>
    </footer>
  </div>

  <script src="popup.js"></script>
</body>
</html>
