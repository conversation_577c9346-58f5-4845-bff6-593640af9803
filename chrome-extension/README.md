# 🔮 Mystic Mirror - Chrome Extension

基于官网 [Mystic Card Mirror](https://mystic-card-mirror-vip.lovable.app/) 的Chrome浏览器插件

## ✨ 功能特色

### 🎯 核心功能
- **每日运势抽卡** - 每天抽取一张塔罗牌获得运势指引
- **AI智能解读** - 使用DeepSeek-V3模型生成个性化解读
- **具体生活建议** - 包含颜色、地点、时间、行动指导
- **正念提醒** - 日行一善和正心正念的温馨提醒

### 🔐 用户系统
- **统一登录** - 与官网共享用户账户和订阅状态
- **订阅同步** - 支持免费用户、试用用户、高级会员
- **一键跳转** - 直接打开官网进行注册、登录、升级

### 📱 用户体验
- **每日提醒** - 上午9点智能提醒抽取运势
- **双语支持** - 中文/英文界面切换
- **历史记录** - 查看过往的塔罗解读
- **响应式设计** - 适配不同屏幕尺寸

## 🚀 安装方法

### 开发者模式安装
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `chrome-extension` 文件夹
6. 插件安装完成！

### 使用方法
1. 点击浏览器工具栏中的插件图标
2. 首次使用需要登录（会跳转到官网）
3. 登录后返回插件，点击"抽取今日运势"
4. 查看AI生成的详细解读和生活建议

## 📋 文件结构

```
chrome-extension/
├── manifest.json          # 插件配置文件
├── popup.html             # 弹窗界面
├── popup.js               # 主要逻辑
├── styles.css             # 样式文件
├── background.js          # 后台脚本
├── icons/                 # 图标文件夹
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md              # 说明文档
```

## 🔧 技术实现

### API集成
- **Supabase** - 用户认证和数据存储
- **Hugging Face** - DeepSeek-V3模型API调用
- **Chrome Extension API** - 浏览器扩展功能

### 数据同步
- 与官网共享用户登录状态
- 订阅信息实时同步
- 本地存储每日运势数据

### 安全性
- 使用官网相同的API密钥
- 遵循Chrome扩展安全策略
- 用户数据加密存储

## 🎨 界面设计

### 视觉风格
- **神秘主题** - 深色渐变背景，紫色主色调
- **现代UI** - 圆角卡片、毛玻璃效果
- **响应式** - 适配380px宽度的弹窗

### 交互体验
- **流畅动画** - 卡牌翻转、加载动画
- **即时反馈** - 按钮状态、加载提示
- **直观导航** - 清晰的功能分区

## 📊 功能详情

### 每日运势解读包含：
1. **🔮 运势分析** - 整体运势趋势
2. **🎨 幸运颜色** - 今日推荐穿着颜色
3. **📍 适宜地点** - 适合活动的场所类型
4. **⏰ 最佳时间** - 做重要事情的时间段
5. **✨ 行动指导** - 具体的行动建议
6. **🙏 日行一善** - 积德行善的建议
7. **💫 正心正念** - 积极正面的思维提醒

### 用户权限：
- **免费用户** - 基础解读功能
- **试用用户** - 完整功能3天体验
- **高级会员** - 全功能无限制使用

## 🔄 与官网的关系

### 数据同步
- 用户在官网登录后，插件自动同步登录状态
- 订阅状态实时更新
- 历史记录在官网和插件间共享

### 功能互补
- **插件** - 快速每日运势，便捷访问
- **官网** - 完整塔罗功能，详细解读
- **无缝切换** - 一键跳转，体验连贯

## 🛠️ 开发说明

### 环境要求
- Chrome 88+ 浏览器
- Manifest V3 支持
- 网络连接（API调用）

### 配置修改
如需修改API配置，请编辑 `popup.js` 中的 `CONFIG` 对象：

```javascript
const CONFIG = {
  WEBSITE_URL: 'https://mystic-card-mirror-vip.lovable.app',
  SUPABASE_URL: 'https://elwmthdxnsyzfvlunvmo.supabase.co',
  HF_API_KEY: 'your-huggingface-api-key',
  HF_API_URL: 'https://router.huggingface.co/novita/v3/openai/chat/completions'
};
```

## 📞 支持与反馈

- **官网**: https://mystic-card-mirror-vip.lovable.app/
- **邮箱**: <EMAIL>
- **微信**: zhidasuc
- **Instagram**: @mystic_mirror_vip

## 📄 版本信息

- **当前版本**: 1.0.0
- **发布日期**: 2024年5月
- **兼容性**: Chrome 88+, Edge 88+

---

*🔮 愿塔罗的智慧为您的每一天带来指引与启发 ✨*
