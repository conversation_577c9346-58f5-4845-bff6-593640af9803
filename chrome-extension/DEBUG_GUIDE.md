# Chrome Extension Debug Guide

## 🔍 问题诊断和修复

### 问题1: History无法显示抽牌历史

#### 可能原因：
1. **数据库保存失败** - 插件抽的牌没有上传到后端
2. **认证问题** - access_token过期或无效
3. **API权限问题** - Supabase权限配置

#### 调试步骤：
1. **打开Chrome开发者工具**
   - 右键点击插件图标 → 检查弹出窗口
   - 或者在插件管理页面点击"检查视图"

2. **查看控制台日志**
   ```
   🔮 Mystic Mirror Extension 初始化...
   👤 当前用户: {id: "...", email: "...", access_token: "..."}
   💾 开始保存解读到数据库...
   📝 准备保存的数据: {...}
   📡 数据库响应状态: 201
   ✅ 保存成功，返回数据: [...]
   ```

3. **检查错误信息**
   - ❌ 保存解读失败: 用户未登录
   - ❌ API请求失败: 401 - Unauthorized
   - ❌ 保存解读失败: 403 - Forbidden

### 问题2: AI解读没有生成

#### 可能原因：
1. **API密钥问题** - Hugging Face API密钥无效
2. **网络问题** - 请求超时或失败
3. **模型问题** - DeepSeek模型不可用
4. **解析问题** - AI返回的文本格式不正确

#### 调试步骤：
1. **查看AI生成日志**
   ```
   🤖 开始生成AI解读...
   📝 使用的提示词: ...
   📡 API响应状态: 200
   ✅ AI响应数据: {...}
   📖 AI生成的文本: ...
   🔍 解析后的解读: {...}
   ```

2. **检查常见错误**
   - ❌ API请求失败: 401 - Invalid API key
   - ❌ API请求失败: 429 - Rate limit exceeded
   - ❌ AI响应格式错误

## 🔧 修复方案

### 修复1: 数据库保存问题

#### 检查用户认证：
```javascript
// 在控制台中运行
console.log('Current User:', currentUser);
console.log('Access Token:', currentUser?.access_token);
```

#### 手动测试API：
```javascript
// 测试数据库连接
fetch('https://elwmthdxnsyzfvlunvmo.supabase.co/rest/v1/reading_history', {
  headers: {
    'Authorization': `Bearer ${currentUser.access_token}`,
    'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    'Content-Type': 'application/json'
  }
}).then(r => r.json()).then(console.log);
```

### 修复2: AI解读生成问题

#### 检查API配置：
```javascript
// 检查配置
console.log('HF API URL:', CONFIG.HF_API_URL);
console.log('HF API Key:', CONFIG.HF_API_KEY.substring(0, 10) + '...');
```

#### 手动测试AI API：
```javascript
// 测试AI API
fetch(CONFIG.HF_API_URL, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${CONFIG.HF_API_KEY}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    model: 'deepseek/deepseek-v3-turbo',
    messages: [{ role: 'user', content: 'Hello' }],
    max_tokens: 100
  })
}).then(r => r.json()).then(console.log);
```

## 📊 调试日志说明

### 正常流程日志：
```
🔮 Mystic Mirror Extension 初始化...
✅ 插件初始化完成
🎯 开始生成AI解读...
🤖 开始生成AI解读...
📝 使用的提示词: 作为专业塔罗师...
📡 API响应状态: 200
✅ AI响应数据: {choices: [...]}
📖 AI生成的文本: 运势分析：今日整体运势...
🔍 开始解析AI文本: ...
✅ 解析完成的sections: {analysis: "...", ...}
✅ AI解读生成完成: {...}
📺 显示AI解读: {...}
💾 开始保存到数据库...
👤 当前用户: {id: "...", ...}
📝 准备保存的数据: {...}
📡 数据库响应状态: 201
✅ 保存成功，返回数据: [...]
🆔 解读ID: abc123
💾 本地存储保存完成
🎉 抽卡流程完成！
```

### 错误情况日志：
```
❌ AI解读生成失败: Error: API请求失败: 401
🔄 使用默认解读...
❌ 保存解读失败: 403 - Forbidden
⚠️ 数据库保存失败，但继续流程
```

## 🚀 测试步骤

### 完整测试流程：
1. **加载插件** - 确保最新代码已加载
2. **打开开发者工具** - 监控控制台日志
3. **登录账户** - 确保用户已认证
4. **抽取卡牌** - 观察完整流程日志
5. **检查历史** - 在官网查看是否有新记录
6. **测试评分** - 点击星星评分
7. **测试返回** - 点击返回主页按钮

### 预期结果：
- ✅ AI解读正常生成和显示
- ✅ 数据保存到数据库成功
- ✅ 官网History页面显示新记录
- ✅ 评分功能正常工作
- ✅ 返回主页功能正常

## 🔗 相关链接

- **官网History**: https://mystic-card-mirror-vip.lovable.app/profile
- **Supabase Dashboard**: https://elwmthdxnsyzfvlunvmo.supabase.co
- **Hugging Face API**: https://router.huggingface.co/novita/v3/openai/chat/completions

## 📞 如果问题仍然存在

请提供以下信息：
1. **完整的控制台日志**
2. **错误发生的具体步骤**
3. **用户账户信息**（邮箱）
4. **浏览器版本和操作系统**

这样我可以进一步诊断和修复问题。
