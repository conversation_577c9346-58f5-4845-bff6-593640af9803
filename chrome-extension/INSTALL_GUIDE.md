# 🔮 Mystic Mirror Chrome插件 - 安装使用指南

## 📦 安装步骤

### 方法一：开发者模式安装（推荐）

1. **下载插件文件**
   - 确保您有完整的 `chrome-extension` 文件夹
   - 包含所有必要文件：manifest.json, popup.html, popup.js, styles.css, background.js

2. **打开Chrome扩展管理页面**
   - 在Chrome地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 点击页面右上角的"开发者模式"开关
   - 确保开关处于开启状态（蓝色）

4. **加载插件**
   - 点击"加载已解压的扩展程序"按钮
   - 选择 `chrome-extension` 文件夹
   - 点击"选择文件夹"

5. **验证安装**
   - 插件应该出现在扩展程序列表中
   - 浏览器工具栏会显示插件图标
   - 状态显示为"已启用"

### 方法二：拖拽安装

1. **打开扩展管理页面**
   - 访问 `chrome://extensions/`
   - 启用"开发者模式"

2. **拖拽安装**
   - 将整个 `chrome-extension` 文件夹拖拽到扩展管理页面
   - Chrome会自动识别并安装插件

## 🎯 首次使用

### 1. 插件激活
- 安装完成后，点击工具栏中的插件图标
- 首次使用会显示登录提示

### 2. 用户登录
- 点击"登录"按钮会跳转到官网
- 在官网完成登录或注册
- 返回插件，用户状态会自动同步

### 3. 抽取运势
- 登录后点击"抽取今日运势"按钮
- 等待AI生成个性化解读
- 查看详细的生活建议和正念提醒

## 🔧 功能使用

### 每日运势抽卡
- **时间限制**: 每天只能抽取一次
- **重置时间**: 每日午夜12点重置
- **卡牌类型**: 22张大阿卡纳塔罗牌
- **解读内容**: 7个维度的详细分析

### 用户权限
- **免费用户**: 基础解读功能
- **试用用户**: 3天完整功能体验
- **高级会员**: 无限制使用所有功能

### 快捷功能
- **🌐 语言切换**: 中文/英文界面
- **🔗 官网跳转**: 一键打开官网
- **📊 历史记录**: 查看过往解读
- **⚙️ 设置**: 个性化配置

## 🔔 通知设置

### 每日提醒
- **默认时间**: 上午9:00
- **提醒内容**: 抽取每日运势
- **操作选项**: 立即抽卡 / 稍后提醒

### 通知管理
- 可在Chrome通知设置中管理
- 支持完全关闭通知功能

## 🛠️ 故障排除

### 常见问题

**Q: 插件图标不显示？**
A: 
- 检查是否正确启用了插件
- 尝试刷新浏览器或重启Chrome
- 确认插件文件完整

**Q: 无法登录或同步用户状态？**
A:
- 确保网络连接正常
- 检查是否在官网正确登录
- 清除浏览器缓存后重试

**Q: AI解读生成失败？**
A:
- 检查网络连接
- 稍后重试（可能是API限制）
- 确认API密钥配置正确

**Q: 今日已抽卡但想重新抽取？**
A:
- 每日只能抽取一次，这是设计限制
- 可以等到第二天重新抽取
- 或访问官网使用完整塔罗功能

### 重置插件
如果遇到严重问题，可以重置插件：

1. 在扩展管理页面点击"详细信息"
2. 点击"扩展程序选项"
3. 清除所有数据
4. 重新登录

### 卸载插件
1. 访问 `chrome://extensions/`
2. 找到 Mystic Mirror 插件
3. 点击"移除"按钮
4. 确认卸载

## 📱 移动端支持

### Chrome移动版
- 目前Chrome移动版不支持扩展程序
- 建议直接访问官网：https://mystic-card-mirror-vip.lovable.app/
- 官网已优化移动端体验

### 替代方案
- 将官网添加到手机主屏幕
- 使用浏览器书签快速访问
- 关注官网的PWA版本更新

## 🔄 更新说明

### 自动更新
- 插件会自动检查更新
- 重要更新会显示通知
- 无需手动干预

### 手动更新
1. 下载最新版本文件
2. 在扩展管理页面点击"重新加载"
3. 或删除旧版本后重新安装

## 📞 技术支持

### 联系方式
- **邮箱**: <EMAIL>
- **微信**: zhidasuc
- **官网**: https://mystic-card-mirror-vip.lovable.app/support

### 反馈渠道
- 通过官网联系表单
- 发送邮件详细描述问题
- 包含浏览器版本和错误截图

### 开发者信息
- 基于官网API开发
- 使用Chrome Extension Manifest V3
- 遵循最新的浏览器安全标准

---

*🔮 感谢使用 Mystic Mirror Chrome插件！愿塔罗的智慧为您的每一天带来指引 ✨*
