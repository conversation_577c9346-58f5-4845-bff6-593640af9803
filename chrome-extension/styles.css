/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 380px;
  min-height: 500px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: #ffffff;
  overflow-x: hidden;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 500px;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(139, 92, 246, 0.3);
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  width: 24px;
  height: 24px;
}

.logo h1 {
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(45deg, #8b5cf6, #ec4899);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-primary {
  background: linear-gradient(45deg, #8b5cf6, #ec4899);
  color: white;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(139, 92, 246, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(139, 92, 246, 0.3);
}

.btn-secondary:hover {
  background: rgba(139, 92, 246, 0.2);
}

.btn-premium {
  background: linear-gradient(45deg, #f59e0b, #f97316);
  color: white;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.btn-icon {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background: rgba(139, 92, 246, 0.3);
}

.btn-text {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.btn-text:hover {
  color: #8b5cf6;
}

/* 用户区域 */
.user-section {
  padding: 16px;
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
}

/* 认证表单样式 */
.auth-form {
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(139, 92, 246, 0.3);
}

.auth-form h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  text-align: center;
  color: #8b5cf6;
}

.form-group {
  margin-bottom: 12px;
}

.form-group input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid rgba(139, 92, 246, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  transition: all 0.2s ease;
}

.form-group input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-group input:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

.form-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.form-actions .btn {
  flex: 1;
}

.form-switch {
  text-align: center;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.form-switch .btn-text {
  color: #8b5cf6;
  font-weight: 500;
}

.login-prompt {
  text-align: center;
}

.login-text {
  margin-bottom: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.login-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-email {
  font-size: 14px;
  font-weight: 500;
}

.user-plan {
  font-size: 12px;
  color: #8b5cf6;
  background: rgba(139, 92, 246, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  width: fit-content;
}

/* 主要内容 */
.main-content {
  flex: 1;
  padding: 16px;
}

.daily-fortune {
  margin-bottom: 20px;
}

.fortune-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.fortune-header h2 {
  font-size: 18px;
  font-weight: 600;
}

.fortune-date {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

/* 塔罗牌样式 */
.tarot-card {
  margin-bottom: 16px;
}

.card-placeholder {
  text-align: center;
  padding: 24px;
  border: 2px dashed rgba(139, 92, 246, 0.3);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
}

.card-back {
  font-size: 48px;
  margin-bottom: 12px;
}

.card-result {
  text-align: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(139, 92, 246, 0.3);
}

.card-image img {
  width: 120px;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.card-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.card-position {
  font-size: 12px;
  color: #8b5cf6;
  background: rgba(139, 92, 246, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
}

.btn-draw {
  width: 100%;
  padding: 12px;
  font-size: 16px;
  position: relative;
}

/* AI解读样式 */
.ai-reading {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(139, 92, 246, 0.3);
  margin-bottom: 16px;
}

.ai-reading h3 {
  font-size: 16px;
  margin-bottom: 16px;
  color: #8b5cf6;
}

.reading-section {
  margin-bottom: 16px;
}

.reading-section h4 {
  font-size: 14px;
  margin-bottom: 8px;
  color: #ec4899;
}

.reading-text {
  font-size: 13px;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.9);
}

.advice-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.advice-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.advice-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.advice-content {
  flex: 1;
}

.advice-content strong {
  font-size: 12px;
  color: #8b5cf6;
  display: block;
  margin-bottom: 4px;
}

.advice-content p {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.3;
}

/* 正念提醒 */
.mindfulness {
  background: linear-gradient(45deg, rgba(139, 92, 246, 0.1), rgba(236, 72, 153, 0.1));
  padding: 12px;
  border-radius: 8px;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.good-deed, .positive-thought {
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.9);
}

.good-deed::before {
  content: "🌟 ";
}

.positive-thought::before {
  content: "💫 ";
}

/* 升级提示 */
.upgrade-prompt {
  background: linear-gradient(45deg, rgba(245, 158, 11, 0.1), rgba(249, 115, 22, 0.1));
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  margin-bottom: 16px;
}

.upgrade-content h3 {
  font-size: 16px;
  margin-bottom: 8px;
  color: #f59e0b;
}

.upgrade-content p {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 12px;
}

/* 底部 */
.footer {
  padding: 12px 16px;
  border-top: 1px solid rgba(139, 92, 246, 0.2);
  background: rgba(255, 255, 255, 0.05);
}

.footer-links {
  display: flex;
  justify-content: space-around;
}

/* 加载动画 */
.loading {
  display: flex;
  align-items: center;
  gap: 8px;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 工具类 */
.hidden {
  display: none !important;
}

/* 响应式调整 */
@media (max-width: 400px) {
  body {
    width: 320px;
  }

  .advice-grid {
    grid-template-columns: 1fr;
  }
}
