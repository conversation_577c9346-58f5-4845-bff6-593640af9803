// 配置常量
const CONFIG = {
  WEBSITE_URL: 'https://mystic-card-mirror-vip.lovable.app',
  SUPABASE_URL: 'https://elwmthdxnsyzfvlunvmo.supabase.co',
  SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsd210aGR4bnN5emZ2bHVudm1vIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxNzg5NjMsImV4cCI6MjA2Mzc1NDk2M30.bOQJV_fApPsev2ZCg5liq15rIhD7bzdrhWELM1BCGnk',
  HF_API_KEY: '*************************************',
  HF_API_URL: 'https://router.huggingface.co/novita/v3/openai/chat/completions'
};

// 全局状态
let currentUser = null;
let currentLanguage = 'en'; // 默认英语
let todayReading = null;

// 语言配置
const LANGUAGES = {
  zh: {
    // 用户界面
    loginText: '登录获取每日塔罗运势',
    loginBtn: '登录',
    registerBtn: '注册',
    logoutBtn: '退出',

    // 表单
    loginFormTitle: '用户登录',
    registerFormTitle: '用户注册',
    emailPlaceholder: '邮箱地址',
    passwordPlaceholder: '密码',
    passwordPlaceholderReg: '密码（至少6位）',
    confirmPasswordPlaceholder: '确认密码',
    loginSubmitText: '登录',
    registerSubmitText: '注册',
    backBtn: '返回',
    noAccountText: '还没有账户？',
    hasAccountText: '已有账户？',
    switchToRegister: '立即注册',
    switchToLogin: '立即登录',

    // 主要功能
    fortuneTitle: '今日运势',
    drawBtnText: '抽取今日运势',
    drawBtnTextDrawn: '今日运势已抽取',
    drawBtnLoading: '解读中...',

    // 用户计划
    planFree: '免费用户',
    planTrial: '试用中',
    planMonthly: '高级会员',

    // 加载状态
    loginLoading: '登录中...',
    registerLoading: '注册中...',

    // 底部按钮
    historyBtn: '历史记录',
    settingsBtn: '设置',
    helpBtn: '帮助',

    // 日期和标题
    logoTitle: '神秘镜像',

    // 提示信息
    loginFirst: '请先登录',
    loginSuccess: '登录成功',
    registerSuccess: '注册成功！获得3天免费试用',
    loginFailed: '登录失败，请检查邮箱和密码',
    registerFailed: '注册失败，请稍后重试',
    fillAllFields: '请填写所有字段',
    passwordMismatch: '两次输入的密码不一致',
    passwordTooShort: '密码至少需要6位',
    fillEmailPassword: '请填写邮箱和密码'
  },
  en: {
    // 用户界面
    loginText: 'Login to get daily tarot fortune',
    loginBtn: 'Login',
    registerBtn: 'Register',
    logoutBtn: 'Logout',

    // 表单
    loginFormTitle: 'User Login',
    registerFormTitle: 'User Registration',
    emailPlaceholder: 'Email Address',
    passwordPlaceholder: 'Password',
    passwordPlaceholderReg: 'Password (min 6 chars)',
    confirmPasswordPlaceholder: 'Confirm Password',
    loginSubmitText: 'Login',
    registerSubmitText: 'Register',
    backBtn: 'Back',
    noAccountText: 'No account yet?',
    hasAccountText: 'Already have account?',
    switchToRegister: 'Register Now',
    switchToLogin: 'Login Now',

    // 主要功能
    fortuneTitle: 'Daily Fortune',
    drawBtnText: 'Draw Daily Fortune',
    drawBtnTextDrawn: 'Today\'s Fortune Drawn',
    drawBtnLoading: 'Reading...',

    // 用户计划
    planFree: 'Free User',
    planTrial: 'Trial',
    planMonthly: 'Premium',

    // 加载状态
    loginLoading: 'Logging in...',
    registerLoading: 'Registering...',

    // 底部按钮
    historyBtn: 'History',
    settingsBtn: 'Settings',
    helpBtn: 'Help',

    // 日期和标题
    logoTitle: 'Mystic Mirror',

    // 提示信息
    loginFirst: 'Please login first',
    loginSuccess: 'Login successful',
    registerSuccess: 'Registration successful! 3-day free trial granted',
    loginFailed: 'Login failed, please check email and password',
    registerFailed: 'Registration failed, please try again later',
    fillAllFields: 'Please fill in all fields',
    passwordMismatch: 'Passwords do not match',
    passwordTooShort: 'Password must be at least 6 characters',
    fillEmailPassword: 'Please fill in email and password'
  }
};

// DOM元素
const elements = {
  // 用户相关
  loginPrompt: document.getElementById('loginPrompt'),
  loginForm: document.getElementById('loginForm'),
  registerForm: document.getElementById('registerForm'),
  userInfo: document.getElementById('userInfo'),
  userEmail: document.getElementById('userEmail'),
  userPlan: document.getElementById('userPlan'),
  loginBtn: document.getElementById('loginBtn'),
  registerBtn: document.getElementById('registerBtn'),
  logoutBtn: document.getElementById('logoutBtn'),

  // 表单元素
  loginFormElement: document.getElementById('loginFormElement'),
  registerFormElement: document.getElementById('registerFormElement'),
  loginEmail: document.getElementById('loginEmail'),
  loginPassword: document.getElementById('loginPassword'),
  registerEmail: document.getElementById('registerEmail'),
  registerPassword: document.getElementById('registerPassword'),
  confirmPassword: document.getElementById('confirmPassword'),
  loginSubmitBtn: document.getElementById('loginSubmitBtn'),
  registerSubmitBtn: document.getElementById('registerSubmitBtn'),
  backToPromptBtn: document.getElementById('backToPromptBtn'),
  backToPromptBtn2: document.getElementById('backToPromptBtn2'),
  switchToRegisterBtn: document.getElementById('switchToRegisterBtn'),
  switchToLoginBtn: document.getElementById('switchToLoginBtn'),

  // 表单文本元素
  loginText: document.getElementById('loginText'),
  loginFormTitle: document.getElementById('loginFormTitle'),
  registerFormTitle: document.getElementById('registerFormTitle'),
  loginSubmitText: document.getElementById('loginSubmitText'),
  registerSubmitText: document.getElementById('registerSubmitText'),
  loginSubmitLoading: document.getElementById('loginSubmitLoading'),
  registerSubmitLoading: document.getElementById('registerSubmitLoading'),
  noAccountText: document.getElementById('noAccountText'),
  hasAccountText: document.getElementById('hasAccountText'),

  // 主要功能
  drawCardBtn: document.getElementById('drawCardBtn'),
  drawBtnText: document.getElementById('drawBtnText'),
  drawBtnLoading: document.getElementById('drawBtnLoading'),
  cardPlaceholder: document.getElementById('cardPlaceholder'),
  cardResult: document.getElementById('cardResult'),
  cardImg: document.getElementById('cardImg'),
  cardName: document.getElementById('cardName'),
  cardPosition: document.getElementById('cardPosition'),
  aiReading: document.getElementById('aiReading'),

  // AI解读内容
  fortuneAnalysis: document.getElementById('fortuneAnalysis'),
  luckyColor: document.getElementById('luckyColor'),
  luckyLocation: document.getElementById('luckyLocation'),
  luckyTime: document.getElementById('luckyTime'),
  actionGuide: document.getElementById('actionGuide'),
  dailyGoodDeed: document.getElementById('dailyGoodDeed'),
  positiveThought: document.getElementById('positiveThought'),

  // 其他按钮
  languageBtn: document.getElementById('languageBtn'),
  openWebsiteBtn: document.getElementById('openWebsiteBtn'),
  upgradeBtn: document.getElementById('upgradeBtn'),
  upgradePrompt: document.getElementById('upgradePrompt'),

  // 日期显示
  fortuneDate: document.getElementById('fortuneDate'),
  fortuneTitle: document.getElementById('fortuneTitle'),

  // 标题和底部按钮
  logoTitle: document.getElementById('logoTitle'),
  historyBtn: document.getElementById('historyBtn'),
  settingsBtn: document.getElementById('settingsBtn'),
  helpBtn: document.getElementById('helpBtn')
};

// 塔罗牌数据
const TAROT_CARDS = [
  { name: "愚者", nameEn: "The Fool", meaning: "新开始、冒险、纯真", image: "https://upload.wikimedia.org/wikipedia/commons/9/90/RWS_Tarot_00_Fool.jpg" },
  { name: "魔术师", nameEn: "The Magician", meaning: "创造力、技能、意志力", image: "https://upload.wikimedia.org/wikipedia/commons/d/de/RWS_Tarot_01_Magician.jpg" },
  { name: "女祭司", nameEn: "The High Priestess", meaning: "直觉、神秘、内在智慧", image: "https://upload.wikimedia.org/wikipedia/commons/8/88/RWS_Tarot_02_High_Priestess.jpg" },
  { name: "皇后", nameEn: "The Empress", meaning: "丰饶、母性、创造", image: "https://upload.wikimedia.org/wikipedia/commons/d/d2/RWS_Tarot_03_Empress.jpg" },
  { name: "皇帝", nameEn: "The Emperor", meaning: "权威、结构、控制", image: "https://upload.wikimedia.org/wikipedia/commons/c/c3/RWS_Tarot_04_Emperor.jpg" },
  { name: "教皇", nameEn: "The Hierophant", meaning: "传统、精神指导、学习", image: "https://upload.wikimedia.org/wikipedia/commons/8/8d/RWS_Tarot_05_Hierophant.jpg" },
  { name: "恋人", nameEn: "The Lovers", meaning: "爱情、选择、和谐", image: "https://upload.wikimedia.org/wikipedia/commons/3/3a/RWS_Tarot_06_Lovers.jpg" },
  { name: "战车", nameEn: "The Chariot", meaning: "胜利、意志力、控制", image: "https://upload.wikimedia.org/wikipedia/commons/9/9b/RWS_Tarot_07_Chariot.jpg" },
  { name: "力量", nameEn: "Strength", meaning: "内在力量、勇气、耐心", image: "https://upload.wikimedia.org/wikipedia/commons/f/f5/RWS_Tarot_08_Strength.jpg" },
  { name: "隐者", nameEn: "The Hermit", meaning: "内省、寻找、指导", image: "https://upload.wikimedia.org/wikipedia/commons/4/4d/RWS_Tarot_09_Hermit.jpg" },
  { name: "命运之轮", nameEn: "Wheel of Fortune", meaning: "变化、命运、机会", image: "https://upload.wikimedia.org/wikipedia/commons/3/3c/RWS_Tarot_10_Wheel_of_Fortune.jpg" },
  { name: "正义", nameEn: "Justice", meaning: "公正、平衡、真理", image: "https://upload.wikimedia.org/wikipedia/commons/e/e0/RWS_Tarot_11_Justice.jpg" },
  { name: "倒吊人", nameEn: "The Hanged Man", meaning: "牺牲、等待、新视角", image: "https://upload.wikimedia.org/wikipedia/commons/2/2b/RWS_Tarot_12_Hanged_Man.jpg" },
  { name: "死神", nameEn: "Death", meaning: "转变、结束、重生", image: "https://upload.wikimedia.org/wikipedia/commons/d/d7/RWS_Tarot_13_Death.jpg" },
  { name: "节制", nameEn: "Temperance", meaning: "平衡、耐心、调和", image: "https://upload.wikimedia.org/wikipedia/commons/f/f8/RWS_Tarot_14_Temperance.jpg" },
  { name: "恶魔", nameEn: "The Devil", meaning: "束缚、诱惑、物质", image: "https://upload.wikimedia.org/wikipedia/commons/5/55/RWS_Tarot_15_Devil.jpg" },
  { name: "塔", nameEn: "The Tower", meaning: "突变、破坏、启示", image: "https://upload.wikimedia.org/wikipedia/commons/5/53/RWS_Tarot_16_Tower.jpg" },
  { name: "星星", nameEn: "The Star", meaning: "希望、灵感、指引", image: "https://upload.wikimedia.org/wikipedia/commons/d/db/RWS_Tarot_17_Star.jpg" },
  { name: "月亮", nameEn: "The Moon", meaning: "幻象、直觉、潜意识", image: "https://upload.wikimedia.org/wikipedia/commons/7/7f/RWS_Tarot_18_Moon.jpg" },
  { name: "太阳", nameEn: "The Sun", meaning: "成功、活力、快乐", image: "https://upload.wikimedia.org/wikipedia/commons/1/17/RWS_Tarot_19_Sun.jpg" },
  { name: "审判", nameEn: "Judgement", meaning: "重生、觉醒、宽恕", image: "https://upload.wikimedia.org/wikipedia/commons/d/dd/RWS_Tarot_20_Judgement.jpg" },
  { name: "世界", nameEn: "The World", meaning: "完成、成就、圆满", image: "https://upload.wikimedia.org/wikipedia/commons/f/ff/RWS_Tarot_21_World.jpg" }
];

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
  console.log('🔮 Mystic Mirror Extension 初始化...');

  // 加载保存的语言设置
  const result = await chrome.storage.local.get(['language']);
  if (result.language) {
    currentLanguage = result.language;
  }

  // 设置今日日期
  updateDateDisplay();

  // 检查用户登录状态
  await checkUserSession();

  // 检查今日是否已抽卡
  await checkTodayReading();

  // 绑定事件监听器
  bindEventListeners();

  // 初始化语言界面
  updateLanguageUI();

  console.log('✅ 插件初始化完成');
});

// 更新日期显示
function updateDateDisplay() {
  const today = new Date();
  const dateStr = today.toLocaleDateString('zh-CN', {
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  });
  elements.fortuneDate.textContent = dateStr;
}

// 检查用户会话
async function checkUserSession() {
  try {
    const result = await chrome.storage.local.get(['userSession']);
    if (result.userSession) {
      currentUser = result.userSession;
      updateUserUI();
    } else {
      showLoginPrompt();
    }
  } catch (error) {
    console.error('检查用户会话失败:', error);
    showLoginPrompt();
  }
}

// 更新用户界面
function updateUserUI() {
  if (currentUser) {
    elements.loginPrompt.classList.add('hidden');
    elements.loginForm.classList.add('hidden');
    elements.registerForm.classList.add('hidden');
    elements.userInfo.classList.remove('hidden');
    elements.userEmail.textContent = currentUser.email;

    // 更新用户计划显示（使用双语）
    updateUserPlanText();
  } else {
    showLoginPrompt();
  }
}

// 显示登录提示
function showLoginPrompt() {
  elements.loginPrompt.classList.remove('hidden');
  elements.userInfo.classList.add('hidden');
}

// 检查今日运势
async function checkTodayReading() {
  try {
    const today = new Date().toDateString();
    const result = await chrome.storage.local.get(['todayReading', 'lastReadingDate']);

    if (result.lastReadingDate === today && result.todayReading) {
      // 今天已经抽过卡了
      todayReading = result.todayReading;
      displayTodayReading();
    } else {
      // 今天还没抽卡
      elements.drawBtnText.textContent = currentLanguage === 'zh' ? '抽取今日运势' : 'Draw Daily Fortune';
    }
  } catch (error) {
    console.error('检查今日运势失败:', error);
  }
}

// 显示今日运势
function displayTodayReading() {
  if (!todayReading) return;

  // 显示卡牌
  elements.cardPlaceholder.classList.add('hidden');
  elements.cardResult.classList.remove('hidden');
  elements.cardImg.src = todayReading.card.image;
  elements.cardImg.alt = todayReading.card.name;
  elements.cardName.textContent = todayReading.card.name;
  elements.cardPosition.textContent = todayReading.position === 'upright' ? '正位' : '逆位';

  // 显示AI解读
  if (todayReading.aiReading) {
    displayAIReading(todayReading.aiReading);
  }

  // 更新按钮状态
  elements.drawBtnText.textContent = currentLanguage === 'zh' ? '今日运势已抽取' : 'Today\'s Fortune Drawn';
  elements.drawCardBtn.disabled = true;
  elements.drawCardBtn.style.opacity = '0.6';
}

// 绑定事件监听器
function bindEventListeners() {
  // 登录/注册按钮
  elements.loginBtn.addEventListener('click', showLoginForm);
  elements.registerBtn.addEventListener('click', showRegisterForm);
  elements.logoutBtn.addEventListener('click', logout);

  // 表单切换按钮
  elements.backToPromptBtn.addEventListener('click', showLoginPrompt);
  elements.backToPromptBtn2.addEventListener('click', showLoginPrompt);
  elements.switchToRegisterBtn.addEventListener('click', showRegisterForm);
  elements.switchToLoginBtn.addEventListener('click', showLoginForm);

  // 表单提交
  elements.loginFormElement.addEventListener('submit', handleLogin);
  elements.registerFormElement.addEventListener('submit', handleRegister);

  // 抽卡按钮
  elements.drawCardBtn.addEventListener('click', drawDailyCard);

  // 其他按钮
  elements.languageBtn.addEventListener('click', toggleLanguage);
  elements.openWebsiteBtn.addEventListener('click', () => openWebsite('/'));
  elements.upgradeBtn.addEventListener('click', () => openWebsite('/pricing'));

  // 底部按钮
  document.getElementById('historyBtn').addEventListener('click', () => openWebsite('/profile'));
  document.getElementById('settingsBtn').addEventListener('click', showSettings);
  document.getElementById('helpBtn').addEventListener('click', () => openWebsite('/support'));
}

// 显示登录表单
function showLoginForm() {
  elements.loginPrompt.classList.add('hidden');
  elements.registerForm.classList.add('hidden');
  elements.loginForm.classList.remove('hidden');
  updateLanguageUI();
}

// 显示注册表单
function showRegisterForm() {
  elements.loginPrompt.classList.add('hidden');
  elements.loginForm.classList.add('hidden');
  elements.registerForm.classList.remove('hidden');
  updateLanguageUI();
}

// 显示登录提示
function showLoginPrompt() {
  elements.loginForm.classList.add('hidden');
  elements.registerForm.classList.add('hidden');
  elements.loginPrompt.classList.remove('hidden');
  elements.userInfo.classList.add('hidden');
  updateLanguageUI();
}

// 处理登录
async function handleLogin(event) {
  event.preventDefault();

  const email = elements.loginEmail.value.trim();
  const password = elements.loginPassword.value;

  if (!email || !password) {
    alert(LANGUAGES[currentLanguage].fillEmailPassword);
    return;
  }

  // 显示加载状态
  elements.loginSubmitText.classList.add('hidden');
  elements.loginSubmitLoading.classList.remove('hidden');
  elements.loginSubmitBtn.disabled = true;

  try {
    const response = await fetch(`${CONFIG.SUPABASE_URL}/auth/v1/token?grant_type=password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': CONFIG.SUPABASE_ANON_KEY
      },
      body: JSON.stringify({
        email: email,
        password: password
      })
    });

    if (!response.ok) {
      throw new Error('登录失败');
    }

    const data = await response.json();

    // 保存用户信息
    currentUser = {
      id: data.user.id,
      email: data.user.email,
      access_token: data.access_token,
      subscription_type: 'free' // 默认值，后续会从数据库获取
    };

    await chrome.storage.local.set({ userSession: currentUser });

    // 获取用户订阅信息
    await fetchUserSubscription();

    // 更新界面
    updateUserUI();

    // 清空表单
    elements.loginEmail.value = '';
    elements.loginPassword.value = '';

    console.log('✅ 登录成功:', currentUser.email);

  } catch (error) {
    console.error('❌ 登录失败:', error);
    alert(LANGUAGES[currentLanguage].loginFailed);
  } finally {
    // 恢复按钮状态
    elements.loginSubmitText.classList.remove('hidden');
    elements.loginSubmitLoading.classList.add('hidden');
    elements.loginSubmitBtn.disabled = false;
  }
}

// 处理注册
async function handleRegister(event) {
  event.preventDefault();

  const email = elements.registerEmail.value.trim();
  const password = elements.registerPassword.value;
  const confirmPassword = elements.confirmPassword.value;

  if (!email || !password || !confirmPassword) {
    alert(LANGUAGES[currentLanguage].fillAllFields);
    return;
  }

  if (password !== confirmPassword) {
    alert(LANGUAGES[currentLanguage].passwordMismatch);
    return;
  }

  if (password.length < 6) {
    alert(LANGUAGES[currentLanguage].passwordTooShort);
    return;
  }

  // 显示加载状态
  elements.registerSubmitText.classList.add('hidden');
  elements.registerSubmitLoading.classList.remove('hidden');
  elements.registerSubmitBtn.disabled = true;

  try {
    const response = await fetch(`${CONFIG.SUPABASE_URL}/auth/v1/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': CONFIG.SUPABASE_ANON_KEY
      },
      body: JSON.stringify({
        email: email,
        password: password
      })
    });

    if (!response.ok) {
      throw new Error('注册失败');
    }

    const data = await response.json();

    if (data.user) {
      // 注册成功，自动登录
      currentUser = {
        id: data.user.id,
        email: data.user.email,
        access_token: data.access_token,
        subscription_type: 'free_trial' // 新用户获得试用期
      };

      await chrome.storage.local.set({ userSession: currentUser });

      // 创建用户订阅记录
      await createUserSubscription();

      // 更新界面
      updateUserUI();

      // 清空表单
      elements.registerEmail.value = '';
      elements.registerPassword.value = '';
      elements.confirmPassword.value = '';

      console.log('✅ 注册成功:', currentUser.email);
      alert(LANGUAGES[currentLanguage].registerSuccess);

    } else {
      throw new Error('注册失败');
    }

  } catch (error) {
    console.error('❌ 注册失败:', error);
    alert(LANGUAGES[currentLanguage].registerFailed);
  } finally {
    // 恢复按钮状态
    elements.registerSubmitText.classList.remove('hidden');
    elements.registerSubmitLoading.classList.add('hidden');
    elements.registerSubmitBtn.disabled = false;
  }
}

// 获取用户订阅信息
async function fetchUserSubscription() {
  if (!currentUser || !currentUser.access_token) return;

  try {
    const response = await fetch(`${CONFIG.SUPABASE_URL}/rest/v1/user_subscriptions?user_id=eq.${currentUser.id}`, {
      headers: {
        'Authorization': `Bearer ${currentUser.access_token}`,
        'apikey': CONFIG.SUPABASE_ANON_KEY,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      if (data && data.length > 0) {
        const subscription = data[0];
        currentUser.subscription_type = subscription.subscription_type;
        currentUser.daily_questions_limit = subscription.daily_questions_limit;
        currentUser.daily_questions_used = subscription.daily_questions_used;

        await chrome.storage.local.set({ userSession: currentUser });
      }
    }
  } catch (error) {
    console.error('获取订阅信息失败:', error);
  }
}

// 创建用户订阅记录
async function createUserSubscription() {
  if (!currentUser || !currentUser.access_token) return;

  try {
    const trialEndDate = new Date();
    trialEndDate.setDate(trialEndDate.getDate() + 3); // 3天试用期

    const subscriptionData = {
      user_id: currentUser.id,
      subscription_type: 'free_trial',
      status: 'active',
      daily_questions_limit: 20,
      daily_questions_used: 0,
      trial_start_date: new Date().toISOString(),
      trial_end_date: trialEndDate.toISOString(),
      trial_used: true
    };

    const response = await fetch(`${CONFIG.SUPABASE_URL}/rest/v1/user_subscriptions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${currentUser.access_token}`,
        'apikey': CONFIG.SUPABASE_ANON_KEY,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(subscriptionData)
    });

    if (response.ok) {
      currentUser.subscription_type = 'free_trial';
      currentUser.daily_questions_limit = 20;
      currentUser.daily_questions_used = 0;
      await chrome.storage.local.set({ userSession: currentUser });
    }
  } catch (error) {
    console.error('创建订阅记录失败:', error);
  }
}

// 打开官网
function openWebsite(path = '/') {
  const url = CONFIG.WEBSITE_URL + path;
  chrome.tabs.create({ url });
}

// 抽取每日卡牌
async function drawDailyCard() {
  if (!currentUser) {
    alert(LANGUAGES[currentLanguage].loginFirst);
    showLoginForm();
    return;
  }

  // 显示加载状态
  elements.drawBtnText.classList.add('hidden');
  elements.drawBtnLoading.classList.remove('hidden');
  elements.drawCardBtn.disabled = true;

  try {
    // 随机选择一张塔罗牌
    const randomCard = TAROT_CARDS[Math.floor(Math.random() * TAROT_CARDS.length)];
    const position = Math.random() > 0.5 ? 'upright' : 'reversed';

    // 显示卡牌
    elements.cardPlaceholder.classList.add('hidden');
    elements.cardResult.classList.remove('hidden');
    elements.cardImg.src = randomCard.image;
    elements.cardImg.alt = randomCard.name;
    elements.cardName.textContent = randomCard.name;
    elements.cardPosition.textContent = position === 'upright' ? '正位' : '逆位';

    // 生成AI解读
    const aiReading = await generateAIReading(randomCard, position);

    // 保存今日运势
    todayReading = {
      card: randomCard,
      position: position,
      aiReading: aiReading,
      date: new Date().toDateString()
    };

    await chrome.storage.local.set({
      todayReading: todayReading,
      lastReadingDate: new Date().toDateString()
    });

    // 显示AI解读
    displayAIReading(aiReading);

    // 更新按钮状态
    elements.drawBtnText.textContent = currentLanguage === 'zh' ? '今日运势已抽取' : 'Today\'s Fortune Drawn';
    elements.drawCardBtn.style.opacity = '0.6';

  } catch (error) {
    console.error('抽卡失败:', error);

    // 恢复按钮状态
    elements.drawBtnText.classList.remove('hidden');
    elements.drawBtnLoading.classList.add('hidden');
    elements.drawCardBtn.disabled = false;

    alert('抽卡失败，请稍后重试');
  }
}

// 生成AI解读
async function generateAIReading(card, position) {
  const prompt = `作为专业塔罗师，为用户解读今日运势。

抽到的卡牌：${card.name} (${position === 'upright' ? '正位' : '逆位'})
卡牌含义：${card.meaning}

请提供以下内容的详细解读：
1. 运势分析：今日整体运势如何
2. 幸运颜色：推荐今日穿着的颜色
3. 适宜地点：今日适合去的地方类型
4. 最佳时间：今日做重要事情的最佳时间段
5. 行动指导：具体的行动建议
6. 日行一善：今日可以做的善事
7. 正心正念：积极正面的思维提醒

请用温暖、积极的语调，给出实用的建议。每个部分控制在50字以内。`;

  try {
    const response = await fetch(CONFIG.HF_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${CONFIG.HF_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'deepseek/deepseek-v3-turbo',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 800,
        temperature: 0.7
      })
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }

    const data = await response.json();
    const aiText = data.choices[0].message.content;

    return parseAIReading(aiText);

  } catch (error) {
    console.error('AI解读生成失败:', error);

    // 返回默认解读
    return getDefaultReading(card, position);
  }
}

// 解析AI解读文本
function parseAIReading(text) {
  const sections = {
    analysis: '',
    luckyColor: '',
    luckyLocation: '',
    luckyTime: '',
    actionGuide: '',
    dailyGoodDeed: '',
    positiveThought: ''
  };

  // 简单的文本解析逻辑
  const lines = text.split('\n').filter(line => line.trim());

  lines.forEach(line => {
    if (line.includes('运势分析') || line.includes('整体运势')) {
      sections.analysis = line.replace(/.*[:：]/, '').trim();
    } else if (line.includes('幸运颜色') || line.includes('颜色')) {
      sections.luckyColor = line.replace(/.*[:：]/, '').trim();
    } else if (line.includes('适宜地点') || line.includes('地点')) {
      sections.luckyLocation = line.replace(/.*[:：]/, '').trim();
    } else if (line.includes('最佳时间') || line.includes('时间')) {
      sections.luckyTime = line.replace(/.*[:：]/, '').trim();
    } else if (line.includes('行动指导') || line.includes('建议')) {
      sections.actionGuide = line.replace(/.*[:：]/, '').trim();
    } else if (line.includes('日行一善') || line.includes('善事')) {
      sections.dailyGoodDeed = line.replace(/.*[:：]/, '').trim();
    } else if (line.includes('正心正念') || line.includes('正面')) {
      sections.positiveThought = line.replace(/.*[:：]/, '').trim();
    }
  });

  return sections;
}

// 获取默认解读
function getDefaultReading(card, position) {
  return {
    analysis: `${card.name}${position === 'upright' ? '正位' : '逆位'}为您带来${card.meaning}的能量，今日运势平稳向好。`,
    luckyColor: '蓝色或紫色，带来平静与智慧的力量',
    luckyLocation: '安静的咖啡厅或图书馆，有助于思考',
    luckyTime: '上午10-12点，精神状态最佳的时段',
    actionGuide: '保持开放的心态，关注内心的声音',
    dailyGoodDeed: '为他人提供帮助或分享一个微笑',
    positiveThought: '相信自己的直觉，每一步都在正确的道路上'
  };
}

// 显示AI解读
function displayAIReading(reading) {
  elements.fortuneAnalysis.textContent = reading.analysis;
  elements.luckyColor.textContent = reading.luckyColor;
  elements.luckyLocation.textContent = reading.luckyLocation;
  elements.luckyTime.textContent = reading.luckyTime;
  elements.actionGuide.textContent = reading.actionGuide;
  elements.dailyGoodDeed.textContent = reading.dailyGoodDeed;
  elements.positiveThought.textContent = reading.positiveThought;

  elements.aiReading.classList.remove('hidden');

  // 检查是否需要显示升级提示
  if (currentUser && currentUser.subscription_type === 'free') {
    elements.upgradePrompt.classList.remove('hidden');
  }
}

// 切换语言
function toggleLanguage() {
  currentLanguage = currentLanguage === 'zh' ? 'en' : 'zh';
  updateLanguageUI();
  chrome.storage.local.set({ language: currentLanguage });
}

// 更新语言界面
function updateLanguageUI() {
  const lang = LANGUAGES[currentLanguage];

  // 用户界面文本
  if (elements.loginText) elements.loginText.textContent = lang.loginText;
  if (elements.loginBtn) elements.loginBtn.textContent = lang.loginBtn;
  if (elements.registerBtn) elements.registerBtn.textContent = lang.registerBtn;
  if (elements.logoutBtn) elements.logoutBtn.textContent = lang.logoutBtn;

  // 表单文本
  if (elements.loginFormTitle) elements.loginFormTitle.textContent = lang.loginFormTitle;
  if (elements.registerFormTitle) elements.registerFormTitle.textContent = lang.registerFormTitle;
  if (elements.loginSubmitText) elements.loginSubmitText.textContent = lang.loginSubmitText;
  if (elements.registerSubmitText) elements.registerSubmitText.textContent = lang.registerSubmitText;
  if (elements.noAccountText) elements.noAccountText.textContent = lang.noAccountText;
  if (elements.hasAccountText) elements.hasAccountText.textContent = lang.hasAccountText;

  // 表单占位符
  if (elements.loginEmail) elements.loginEmail.placeholder = lang.emailPlaceholder;
  if (elements.loginPassword) elements.loginPassword.placeholder = lang.passwordPlaceholder;
  if (elements.registerEmail) elements.registerEmail.placeholder = lang.emailPlaceholder;
  if (elements.registerPassword) elements.registerPassword.placeholder = lang.passwordPlaceholderReg;
  if (elements.confirmPassword) elements.confirmPassword.placeholder = lang.confirmPasswordPlaceholder;

  // 按钮文本
  if (elements.backToPromptBtn) elements.backToPromptBtn.textContent = lang.backBtn;
  if (elements.backToPromptBtn2) elements.backToPromptBtn2.textContent = lang.backBtn;
  if (elements.switchToRegisterBtn) elements.switchToRegisterBtn.textContent = lang.switchToRegister;
  if (elements.switchToLoginBtn) elements.switchToLoginBtn.textContent = lang.switchToLogin;

  // 主要功能
  if (elements.fortuneTitle) elements.fortuneTitle.textContent = lang.fortuneTitle;
  if (elements.drawBtnText) {
    elements.drawBtnText.textContent = todayReading ? lang.drawBtnTextDrawn : lang.drawBtnText;
  }

  // 加载状态文本
  if (elements.loginSubmitLoading) {
    const loadingSpan = elements.loginSubmitLoading.querySelector('span');
    if (loadingSpan) loadingSpan.textContent = lang.loginLoading;
  }
  if (elements.registerSubmitLoading) {
    const loadingSpan = elements.registerSubmitLoading.querySelector('span');
    if (loadingSpan) loadingSpan.textContent = lang.registerLoading;
  }
  if (elements.drawBtnLoading) {
    const loadingSpan = elements.drawBtnLoading.querySelector('span');
    if (loadingSpan) loadingSpan.textContent = lang.drawBtnLoading;
  }

  // 标题和底部按钮
  if (elements.logoTitle) elements.logoTitle.textContent = lang.logoTitle;
  if (elements.historyBtn) elements.historyBtn.textContent = lang.historyBtn;
  if (elements.settingsBtn) elements.settingsBtn.textContent = lang.settingsBtn;
  if (elements.helpBtn) elements.helpBtn.textContent = lang.helpBtn;

  // 更新用户计划显示
  updateUserPlanText();
}

// 更新用户计划文本
function updateUserPlanText() {
  if (!currentUser || !elements.userPlan) return;

  const lang = LANGUAGES[currentLanguage];
  let planText = lang.planFree;

  if (currentUser.subscription_type === 'monthly') {
    planText = lang.planMonthly;
  } else if (currentUser.subscription_type === 'free_trial') {
    planText = lang.planTrial;
  }

  elements.userPlan.textContent = planText;
}

// 登出
async function logout() {
  currentUser = null;
  todayReading = null;
  await chrome.storage.local.clear();
  showLoginPrompt();

  // 重置界面
  elements.cardResult.classList.add('hidden');
  elements.cardPlaceholder.classList.remove('hidden');
  elements.aiReading.classList.add('hidden');
  elements.upgradePrompt.classList.add('hidden');
  elements.drawCardBtn.disabled = false;
  elements.drawCardBtn.style.opacity = '1';
}

// 显示设置
function showSettings() {
  alert('设置功能开发中...');
}

// 错误处理
window.addEventListener('error', (event) => {
  console.error('插件错误:', event.error);
});

console.log('🔮 Mystic Mirror Extension 脚本加载完成');
