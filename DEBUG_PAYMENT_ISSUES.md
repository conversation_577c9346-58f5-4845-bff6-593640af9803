# 🔍 支付问题调试指南

## 🚨 当前问题
用户报告：点击升级按钮后显示 "Error: Failed to start payment process"

## 🛠️ 调试步骤

### 1. 检查浏览器控制台
1. 打开浏览器开发者工具 (F12)
2. 切换到 Console 标签
3. 点击升级按钮
4. 查看控制台输出的错误信息

### 2. 使用测试API按钮
我已经在定价页面添加了一个"Test API"按钮：

1. 访问：http://localhost:8080/pricing
2. 登录用户账户
3. 在Premium卡片中点击"Test API"按钮
4. 查看控制台输出和Toast消息

### 3. 检查API密钥
在控制台中运行以下代码：
```javascript
// 检查API密钥配置
import { validateAPIKey } from './src/utils/test-payment.ts';
validateAPIKey();
```

### 4. 手动测试API调用
在控制台中运行：
```javascript
// 手动测试Creem.io API
fetch('https://api.creem.io/v1/checkouts', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'x-api-key': 'creem_2gPDtvmta7jlXaykq0H9Is'
  },
  body: JSON.stringify({
    product_id: 'prod_2Ei8CdkAewpPbGZFz09Cgq',
    success_url: 'https://mystic-card-mirror-vip.lovable.app/payment/success',
    cancel_url: 'https://mystic-card-mirror-vip.lovable.app/payment/cancel',
    customer_email: '<EMAIL>',
    request_id: 'test_user_123'
  })
})
.then(response => {
  console.log('Status:', response.status);
  return response.text();
})
.then(text => {
  console.log('Response:', text);
  try {
    const json = JSON.parse(text);
    console.log('Parsed JSON:', json);
  } catch (e) {
    console.log('Not JSON response');
  }
})
.catch(error => console.error('Error:', error));
```

## 🔍 可能的问题和解决方案

### 1. API密钥问题
**症状：** 401 Unauthorized 错误
**解决方案：**
- 验证API密钥是否正确
- 检查API密钥是否在测试模式
- 确认API密钥格式：`creem_xxxxxxxxxx`

### 2. 产品ID问题
**症状：** 404 Not Found 或产品不存在错误
**解决方案：**
- 验证产品ID：`prod_2Ei8CdkAewpPbGZFz09Cgq`
- 确认产品在Creem.io仪表板中存在
- 检查产品是否在测试模式

### 3. CORS问题
**症状：** CORS policy 错误
**解决方案：**
- 这通常不是问题，因为Creem.io API支持CORS
- 如果遇到，可能需要从后端调用API

### 4. 网络问题
**症状：** Network error 或 fetch failed
**解决方案：**
- 检查网络连接
- 尝试直接访问 https://api.creem.io
- 检查防火墙设置

### 5. 请求格式问题
**症状：** 400 Bad Request 错误
**解决方案：**
- 检查请求体格式
- 验证必需字段
- 确认Content-Type头部

## 📊 预期的API响应

### 成功响应示例：
```json
{
  "id": "ch_xxxxxxxxxx",
  "url": "https://checkout.creem.io/ch_xxxxxxxxxx",
  "status": "pending",
  "product_id": "prod_2Ei8CdkAewpPbGZFz09Cgq"
}
```

### 错误响应示例：
```json
{
  "error": {
    "type": "invalid_request_error",
    "message": "Invalid product_id",
    "code": "product_not_found"
  }
}
```

## 🔧 临时解决方案

如果API调用仍然失败，可以尝试以下临时解决方案：

### 1. 直接使用产品链接
```javascript
// 临时解决方案：直接跳转到产品页面
const directPaymentUrl = `https://www.creem.io/test/payment/prod_2Ei8CdkAewpPbGZFz09Cgq?success_url=${encodeURIComponent('https://mystic-card-mirror-vip.lovable.app/payment/success')}&cancel_url=${encodeURIComponent('https://mystic-card-mirror-vip.lovable.app/payment/cancel')}`;
window.location.href = directPaymentUrl;
```

### 2. 使用不同的API端点
```javascript
// 尝试不同的API版本或端点
const alternativeEndpoint = 'https://api.creem.io/checkouts'; // 无版本号
```

## 📝 调试检查清单

- [ ] 浏览器控制台无JavaScript错误
- [ ] API密钥格式正确且有效
- [ ] 产品ID存在且可访问
- [ ] 网络连接正常
- [ ] 请求头部正确设置
- [ ] 请求体格式正确
- [ ] 用户已登录且有有效邮箱
- [ ] Success/Cancel URL可访问

## 📞 获取帮助

如果问题仍然存在：

1. **收集信息：**
   - 浏览器控制台的完整错误信息
   - 网络请求的详细信息
   - API响应内容

2. **联系支持：**
   - **邮箱：** <EMAIL>
   - **微信：** zhidasuc
   - **Creem.io支持：** <EMAIL>

3. **提供信息：**
   - 错误截图
   - 控制台日志
   - 网络请求详情
   - 使用的浏览器和版本

## 🎯 下一步

1. 首先尝试"Test API"按钮
2. 检查控制台输出
3. 根据错误信息采取相应措施
4. 如果需要，联系技术支持

记住：详细的错误信息是解决问题的关键！🔍
