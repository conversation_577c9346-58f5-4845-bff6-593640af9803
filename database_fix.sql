-- 🔧 Complete Database Schema Fix
-- Execute this in Supabase SQL Editor: https://supabase.com/dashboard/project/elwmthdxnsyzfvlunvmo/sql

-- ============================================================================
-- 1. CHECK CURRENT STATE
-- ============================================================================

-- Check what tables exist
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
    AND table_name IN ('reading_history', 'tarot_readings', 'reading_feedback');

-- Check current foreign key constraints
SELECT
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM
    information_schema.table_constraints AS tc
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name='reading_feedback';

-- ============================================================================
-- 2. ENSURE READING_HISTORY TABLE EXISTS WITH CORRECT STRUCTURE
-- ============================================================================

-- Create reading_history table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.reading_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    question TEXT NOT NULL,
    cards JSONB NOT NULL,
    ai_reading TEXT NOT NULL,
    spread_type VARCHAR(50) DEFAULT 'three_card',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- 3. FIX READING_FEEDBACK FOREIGN KEY
-- ============================================================================

-- Drop any existing foreign key constraint
ALTER TABLE public.reading_feedback
DROP CONSTRAINT IF EXISTS reading_feedback_reading_id_fkey;

-- Add the correct foreign key constraint
ALTER TABLE public.reading_feedback
ADD CONSTRAINT reading_feedback_reading_id_fkey
FOREIGN KEY (reading_id)
REFERENCES public.reading_history(id)
ON DELETE CASCADE;

-- ============================================================================
-- 4. ENSURE PROPER RLS POLICIES
-- ============================================================================

-- Enable RLS on reading_history
ALTER TABLE public.reading_history ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for reading_history
DROP POLICY IF EXISTS "Users can view own reading history" ON public.reading_history;
CREATE POLICY "Users can view own reading history" ON public.reading_history
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert own reading history" ON public.reading_history;
CREATE POLICY "Users can insert own reading history" ON public.reading_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete own reading history" ON public.reading_history;
CREATE POLICY "Users can delete own reading history" ON public.reading_history
    FOR DELETE USING (auth.uid() = user_id);

-- ============================================================================
-- 5. VERIFY THE FIX
-- ============================================================================

-- Check final foreign key constraints
SELECT
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM
    information_schema.table_constraints AS tc
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name='reading_feedback';

-- Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies
WHERE tablename = 'reading_history';
