-- 用户订阅表
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    subscription_type VARCHAR(20) NOT NULL DEFAULT 'free_trial' CHECK (subscription_type IN ('free_trial', 'monthly', 'cancelled')),
    status VARCHAR(20) NOT NULL DEFAULT 'expired' CHECK (status IN ('active', 'expired', 'cancelled')),
    trial_start_date TIMESTAMPTZ,
    trial_end_date TIMESTAMPTZ,
    subscription_start_date TIMESTAMPTZ,
    subscription_end_date TIMESTAMPTZ,
    daily_questions_used INTEGER NOT NULL DEFAULT 0,
    daily_questions_limit INTEGER NOT NULL DEFAULT 1,
    last_question_date TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id)
);

-- 支付历史表
CREATE TABLE IF NOT EXISTS payment_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id VARCHAR(255),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    payment_method VARCHAR(50),
    failure_reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 占卜历史表（可选，用于高级用户）
CREATE TABLE IF NOT EXISTS reading_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    question TEXT NOT NULL,
    cards_drawn JSONB NOT NULL,
    spread_type VARCHAR(50) NOT NULL,
    reading_result TEXT NOT NULL,
    language VARCHAR(2) NOT NULL DEFAULT 'en',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_payment_history_user_id ON payment_history(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_history_status ON payment_history(status);
CREATE INDEX IF NOT EXISTS idx_reading_history_user_id ON reading_history(user_id);
CREATE INDEX IF NOT EXISTS idx_reading_history_created_at ON reading_history(created_at);

-- 创建更新时间戳的函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为user_subscriptions表创建触发器
CREATE TRIGGER update_user_subscriptions_updated_at 
    BEFORE UPDATE ON user_subscriptions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 启用行级安全策略 (RLS)
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE reading_history ENABLE ROW LEVEL SECURITY;

-- 用户订阅表的RLS策略
CREATE POLICY "Users can view own subscription" ON user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own subscription" ON user_subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own subscription" ON user_subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 支付历史表的RLS策略
CREATE POLICY "Users can view own payment history" ON payment_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own payment history" ON payment_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 占卜历史表的RLS策略
CREATE POLICY "Users can view own reading history" ON reading_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own reading history" ON reading_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 创建函数来检查用户是否可以提问
CREATE OR REPLACE FUNCTION can_user_ask_question(user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    subscription_record user_subscriptions%ROWTYPE;
    today_date DATE;
    last_question_date DATE;
BEGIN
    -- 获取用户订阅信息
    SELECT * INTO subscription_record 
    FROM user_subscriptions 
    WHERE user_id = user_uuid;
    
    -- 如果没有订阅记录，返回false
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- 如果是付费用户且状态为active，返回true
    IF subscription_record.subscription_type = 'monthly' AND subscription_record.status = 'active' THEN
        RETURN TRUE;
    END IF;
    
    -- 如果是试用期用户且在试用期内，返回true
    IF subscription_record.subscription_type = 'free_trial' AND subscription_record.status = 'active' THEN
        IF subscription_record.trial_end_date IS NOT NULL AND NOW() <= subscription_record.trial_end_date THEN
            RETURN TRUE;
        END IF;
    END IF;
    
    -- 免费用户检查每日限制
    today_date := CURRENT_DATE;
    
    -- 如果last_question_date为空或者不是今天，可以提问
    IF subscription_record.last_question_date IS NULL THEN
        RETURN TRUE;
    END IF;
    
    last_question_date := DATE(subscription_record.last_question_date);
    
    -- 如果不是今天，可以提问
    IF last_question_date != today_date THEN
        RETURN TRUE;
    END IF;
    
    -- 检查今日使用次数
    RETURN subscription_record.daily_questions_used < subscription_record.daily_questions_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建函数来增加问题计数
CREATE OR REPLACE FUNCTION increment_question_count(user_uuid UUID)
RETURNS VOID AS $$
DECLARE
    subscription_record user_subscriptions%ROWTYPE;
    today_date DATE;
    last_question_date DATE;
    new_count INTEGER;
BEGIN
    -- 获取用户订阅信息
    SELECT * INTO subscription_record 
    FROM user_subscriptions 
    WHERE user_id = user_uuid;
    
    -- 如果没有订阅记录，退出
    IF NOT FOUND THEN
        RETURN;
    END IF;
    
    today_date := CURRENT_DATE;
    
    -- 如果last_question_date为空或者不是今天，重置计数
    IF subscription_record.last_question_date IS NULL THEN
        new_count := 1;
    ELSE
        last_question_date := DATE(subscription_record.last_question_date);
        IF last_question_date != today_date THEN
            new_count := 1;
        ELSE
            new_count := subscription_record.daily_questions_used + 1;
        END IF;
    END IF;
    
    -- 更新记录
    UPDATE user_subscriptions 
    SET 
        daily_questions_used = new_count,
        last_question_date = NOW(),
        updated_at = NOW()
    WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 插入示例数据（可选）
-- 注意：这些是示例数据，实际使用时请根据需要调整
/*
INSERT INTO user_subscriptions (user_id, subscription_type, status, daily_questions_limit) 
VALUES 
    ('00000000-0000-0000-0000-000000000001', 'free_trial', 'expired', 1),
    ('00000000-0000-0000-0000-000000000002', 'monthly', 'active', 999);
*/
