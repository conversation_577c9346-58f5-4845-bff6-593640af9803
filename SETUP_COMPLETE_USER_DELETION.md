# 🗑️ 完整用户删除功能设置指南

## 📋 问题描述

当前用户删除功能只删除了认证账户，但用户数据（订阅、占卜历史等）仍保留在数据库中，导致用户重新登录后数据依然存在。

## ✅ 解决方案

### 1. 在Supabase中执行SQL函数

**步骤：**
1. 登录 [Supabase Dashboard](https://supabase.com/dashboard)
2. 选择您的项目：`elwmthdxnsyzfvlunvmo`
3. 点击左侧菜单的 "SQL Editor"
4. 创建新查询，复制并执行 `database/complete_user_deletion.sql` 中的内容

**SQL函数功能：**
```sql
-- 完整删除用户的所有数据
SELECT complete_user_deletion('user-uuid-here');
```

### 2. 设置Netlify环境变量

**需要在Netlify Dashboard中设置：**

1. 访问 [Netlify Dashboard](https://app.netlify.com)
2. 选择您的站点
3. 进入 Site settings → Environment variables
4. 添加以下变量：

```
SUPABASE_URL=https://elwmthdxnsyzfvlunvmo.supabase.co
SUPABASE_SERVICE_KEY=your_service_role_key_here
```

**获取Service Role Key：**
1. 在Supabase Dashboard中
2. 进入 Settings → API
3. 复制 "service_role" key（不是anon key）
4. 这个key有完整的数据库权限，包括删除auth.users

### 3. 验证功能

**测试步骤：**
1. 访问 `/test-delete-user` 页面
2. 点击 "Test Netlify Functions" 按钮
3. 检查是否显示：
   ```json
   {
     "hasServiceKey": true,
     "serviceKeyLength": 123
   }
   ```

## 🔧 技术实现

### 数据库函数 (`complete_user_deletion`)

**删除顺序（遵循外键约束）：**
1. `reading_feedback` - 占卜反馈
2. `reading_history` - 占卜历史
3. `tarot_readings` - 塔罗记录
4. `payment_history` - 支付历史
5. `user_subscriptions` - 用户订阅
6. `profiles` - 用户资料

**返回结果：**
```json
{
  "reading_feedback": 5,
  "reading_history": 12,
  "tarot_readings": 8,
  "payment_history": 2,
  "user_subscriptions": 1,
  "profiles": 1,
  "total_records_deleted": 29,
  "user_id": "uuid",
  "deleted_at": "2024-01-01T00:00:00Z"
}
```

### Netlify函数 (`delete-user.js`)

**执行流程：**
1. 验证用户身份（auth token）
2. 调用数据库函数删除所有用户数据
3. 使用Service Role删除auth.users记录
4. 返回详细的删除结果

**回退机制：**
- 如果数据库函数失败，使用手动删除
- 如果Service Key未配置，只删除数据表

## 🚨 安全考虑

### Service Role Key安全

**重要提醒：**
- Service Role Key拥有完整数据库权限
- 只在服务器端使用，绝不暴露给前端
- 定期轮换密钥
- 监控使用情况

### 删除确认

**用户删除流程：**
1. 用户在前端确认删除
2. 显示警告：数据将永久删除
3. 调用后端API进行完整删除
4. 删除成功后自动登出

## 🧪 测试指南

### 1. 环境变量测试
```bash
# 访问测试页面
https://your-site.netlify.app/test-delete-user

# 点击 "Test Netlify Functions"
# 应该看到 hasServiceKey: true
```

### 2. 删除功能测试
```bash
# 创建测试用户
# 进行一些操作（占卜、订阅等）
# 在Profile页面点击删除账户
# 验证无法重新登录
```

### 3. 数据验证
```sql
-- 在Supabase SQL Editor中检查
SELECT * FROM user_data_summary WHERE user_id = 'deleted-user-id';
-- 应该返回空结果
```

## 🔍 故障排除

### 常见问题

**1. "hasServiceKey: false"**
- 检查Netlify环境变量是否正确设置
- 确认Service Role Key是否有效

**2. "Database function failed"**
- 检查SQL函数是否已在Supabase中创建
- 验证函数权限是否正确授予

**3. "Cannot delete auth user"**
- 确认Service Role Key权限
- 检查用户ID是否正确

**4. 用户重新登录后数据仍存在**
- 检查删除函数是否成功执行
- 验证所有表是否都被清理

## 📞 支持

如果遇到问题，请检查：
1. Netlify函数日志
2. Supabase日志
3. 浏览器控制台错误

**联系信息：**
- 邮箱：<EMAIL>
- 微信：zhidasuc

## ✅ 完成检查清单

- [ ] 在Supabase中执行SQL函数
- [ ] 在Netlify中设置环境变量
- [ ] 测试函数可用性
- [ ] 验证完整删除功能
- [ ] 确认无法重新登录已删除账户
