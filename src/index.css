
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 30% 10%;
    --foreground: 0 0% 95%;

    --card: 240 30% 12%;
    --card-foreground: 0 0% 95%;

    --popover: 240 30% 12%;
    --popover-foreground: 0 0% 95%;

    --primary: 255 80% 75%;
    --primary-foreground: 0 0% 100%;

    --secondary: 240 20% 20%;
    --secondary-foreground: 0 0% 95%;

    --muted: 240 20% 20%;
    --muted-foreground: 240 10% 70%;

    --accent: 270 75% 60%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 20% 30%;
    --input: 240 20% 30%;
    --ring: 255 80% 75%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  }

  /* Custom tarot card styling */
  .tarot-card {
    @apply relative w-full h-full bg-mystic-purple rounded-lg shadow-lg perspective-700 transform-style-3d transition-transform duration-700;
  }

  .tarot-card-front,
  .tarot-card-back {
    @apply absolute w-full h-full rounded-lg backface-hidden;
  }

  .tarot-card-back {
    @apply bg-mystic-dark transform rotate-y-180;
  }

  .perspective-700 {
    perspective: 700px;
  }

  .transform-style-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }
  
  /* Typewriter animation */
  @keyframes blinkCursor {
    0%, 100% { border-right-color: transparent; }
    50% { border-right-color: #9b87f5; }
  }
  
  .typewriter {
    border-right: 2px solid #9b87f5;
    animation: blinkCursor 0.8s infinite;
    white-space: pre-wrap;
    word-break: break-word;
  }
}

/* Prose styles for AI content */
.prose {
  @apply text-foreground;
  
  & h1, & h2, & h3, & h4, & h5, & h6 {
    @apply text-mystic-purple font-bold my-4;
  }
  
  & h1 {
    @apply text-2xl;
  }
  
  & h2 {
    @apply text-xl;
  }
  
  & h3 {
    @apply text-lg;
  }
  
  & p {
    @apply my-4;
  }
  
  & ul, & ol {
    @apply my-4 pl-6;
  }
  
  & ul {
    @apply list-disc;
  }
  
  & ol {
    @apply list-decimal;
  }
  
  & a {
    @apply text-primary underline hover:text-primary/80;
  }
  
  & blockquote {
    @apply border-l-4 border-mystic-purple/40 pl-4 italic my-4;
  }
}
