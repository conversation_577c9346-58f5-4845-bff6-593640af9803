
// � 安全的API配置 - 从环境变量读取
export const DEEPSEEK_API_KEY = import.meta.env.VITE_DEEPSEEK_API_KEY || "***********************************";
export const DEFAULT_MODEL = "deepseek-chat";
export const API_URL = "https://api.deepseek.com/v1/chat/completions";

// � 安全的API密钥状态检查
console.log('� API Configuration Status:', {
  envVarExists: !!import.meta.env.VITE_DEEPSEEK_API_KEY,
  envVarValue: import.meta.env.VITE_DEEPSEEK_API_KEY ? 'EXISTS' : 'MISSING',
  finalKeyLength: DEEPSEEK_API_KEY?.length,
  isValidFormat: DEEPSEEK_API_KEY?.startsWith('sk-') && DEEPSEEK_API_KEY?.length === 51,
  timestamp: new Date().toISOString()
});

// � 安全的配置验证（不显示完整密钥）
console.log('� Secure API Configuration:', {
  hasApiKey: !!DEEPSEEK_API_KEY,
  keyLength: DEEPSEEK_API_KEY?.length || 0,
  keyPrefix: DEEPSEEK_API_KEY ? DEEPSEEK_API_KEY.substring(0, 8) + '***' : 'NOT_SET',
  model: DEFAULT_MODEL,
  url: API_URL,
  platform: 'Lovable'
});

// 🚨 API密钥检查
if (!DEEPSEEK_API_KEY) {
  console.error('❌ DEEPSEEK_API_KEY not found in environment variables!');
  console.error('📋 Please set VITE_DEEPSEEK_API_KEY in your deployment environment');
}

// 🗑️ 移除环境变量调试，强制使用硬编码配置

// 🗑️ 已移除Hugging Face配置，现在完全使用DeepSeek API

// Creem.io API configuration
export const CREEM_API_KEY = "creem_2gPDtvmta7jlXaykq0H9Is";
export const CREEM_API_URL = "https://api.creem.io/v1";

