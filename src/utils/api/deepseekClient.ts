
import { DEEPSEEK_API_KEY, DEFAULT_MODEL, API_URL } from './config';

// 清理markdown符号的函数 - 优化版本（减少日志）
const cleanMarkdownSymbols = (text: any): string => {
  // 简单的类型检查和转换
  if (text === null || text === undefined) {
    return '';
  }

  // 确保是字符串
  let str: string;
  try {
    str = typeof text === 'string' ? text : String(text);
  } catch (conversionError) {
    console.error('❌ cleanMarkdownSymbols: String conversion failed:', conversionError);
    return '';
  }

  try {
    return str
      // 移除粗体符号 **text** 和 __text__
      .replace(/\*\*(.*?)\*\*/g, '$1')
      .replace(/__(.*?)__/g, '$1')
      // 移除斜体符号 *text* 和 _text_
      .replace(/\*(.*?)\*/g, '$1')
      .replace(/_(.*?)_/g, '$1')
      // 移除标题符号 ###
      .replace(/^#{1,6}\s+/gm, '')
      // 移除列表符号 - 和 * 开头的行
      .replace(/^[\s]*[-*]\s+/gm, '')
      // 移除代码块符号 ```
      .replace(/```[\s\S]*?```/g, '')
      .replace(/`([^`]+)`/g, '$1')
      // 移除链接格式 [text](url)
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
      // 清理多余的空行
      .replace(/\n{3,}/g, '\n\n')
      .trim();

  } catch (error) {
    console.error('❌ Error in cleanMarkdownSymbols:', error);
    return String(text || '');
  }
};

interface Message {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface RequestOptions {
  model: string;
  messages: Message[];
  temperature: number;
  max_tokens: number;
  [key: string]: any;
}

// 流式API调用，支持实时显示
export const callDeepSeekStream = async (
  options: RequestOptions,
  language: 'en' | 'zh' = 'en',
  onChunk?: (chunk: string) => void
): Promise<string> => {
  try {
    const startTime = Date.now();
    console.log("🚀 Streaming API Request started at:", new Date().toISOString());

    // 🔐 安全的API调用验证
    console.log('🚀 Streaming API Call:', {
      url: API_URL,
      model: DEFAULT_MODEL,
      hasValidKey: !!DEEPSEEK_API_KEY && DEEPSEEK_API_KEY.startsWith('sk-')
    });

    // 使用DeepSeek Chat Completions API (流式)
    const response = await fetch(API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${DEEPSEEK_API_KEY}`
      },
      body: JSON.stringify({
        model: DEFAULT_MODEL,
        messages: options.messages,
        temperature: options.temperature,
        max_tokens: options.max_tokens,
        stream: true
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Streaming API error:", errorText);
      throw new Error(`Streaming API request failed: ${errorText}`);
    }

    let fullContent = '';
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    if (reader) {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);

              // Chat Completions流式响应格式
              const content = parsed.choices?.[0]?.delta?.content;

              if (content) {
                fullContent += content;

                // 实时回调，显示内容
                if (onChunk) {
                  try {
                    const cleanedContent = cleanMarkdownSymbols(fullContent);
                    onChunk(cleanedContent);
                  } catch (cleanError) {
                    console.error('❌ Error in streaming callback:', cleanError);
                  }
                }
              }
            } catch (e) {
              // 忽略JSON解析错误（正常情况，因为chunk可能不完整）
            }
          }
        }
      }
    }

    const endTime = Date.now();
    console.log("✅ Streaming completed in", (endTime - startTime) + "ms");

    try {
      const finalResult = cleanMarkdownSymbols(fullContent);
      return finalResult;
    } catch (finalError) {
      console.error("❌ Error in final cleanMarkdownSymbols call:", finalError);
      return fullContent || '';
    }

  } catch (error) {
    console.error("Streaming API error:", error);
    // 如果流式失败，回退到普通API
    return callDeepSeek(options, language);
  }
};

export const callDeepSeek = async (options: RequestOptions, language: 'en' | 'zh' = 'en'): Promise<string> => {
  try {
    const startTime = Date.now();
    console.log("🚀 API Request started at:", new Date().toISOString());
    console.log("Sending request to DeepSeek API:", options);
    console.log("Using model:", DEFAULT_MODEL);

    // 使用DeepSeek Chat Completions API
    const response = await fetch(API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${DEEPSEEK_API_KEY}`
      },
      body: JSON.stringify({
        model: DEFAULT_MODEL,
        messages: options.messages,
        temperature: options.temperature,
        max_tokens: options.max_tokens,
        stream: false
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("DeepSeek API error:", errorText);
      throw new Error(`API request failed with status ${response.status}: ${errorText}`);
    }

    const result = await response.json();
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log("⏱️ API Response received at:", new Date().toISOString());
    console.log("🕐 Total API response time:", responseTime + "ms");
    console.log("DeepSeek API response:", result);

    // 处理Chat Completions响应格式
    if (result.choices && result.choices[0] && result.choices[0].message) {
      const messageContent = result.choices[0].message.content;
      console.log("📝 Message content type:", typeof messageContent);
      console.log("📝 Message content value:", messageContent);

      if (messageContent === null || messageContent === undefined) {
        console.error("❌ API returned null/undefined content");
        return language === 'zh'
          ? "很抱歉，AI暂时无法生成回复。请稍后再试。"
          : "Sorry, AI couldn't generate a response right now. Please try again later.";
      }

      const rawContent = String(messageContent).trim();
      const cleanedContent = cleanMarkdownSymbols(rawContent);
      console.log("Raw content:", rawContent);
      console.log("Cleaned content:", cleanedContent);
      console.log("✅ API call completed successfully in", responseTime + "ms");
      return cleanedContent;
    } else {
      console.error("Unexpected API response structure:", result);
      return language === 'zh'
        ? "很抱歉，我无法生成解读。响应格式不符合预期。"
        : "I'm sorry, but I couldn't generate a reading. The response format was unexpected.";
    }
  } catch (error) {
    console.error("❌ Error calling DeepSeek API:", error);
    console.error("❌ Error details:", {
      message: error.message,
      stack: error.stack,
      name: error.name
    });

    // 提供更详细的错误信息
    const errorMessage = language === 'zh'
      ? `很抱歉，AI服务暂时不可用。错误信息：${error.message || '未知错误'}。请稍后再试。`
      : `I'm sorry, the AI service is temporarily unavailable. Error: ${error.message || 'Unknown error'}. Please try again later.`;

    return errorMessage;
  }
};

