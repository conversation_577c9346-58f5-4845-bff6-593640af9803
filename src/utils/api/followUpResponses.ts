
import { callDeepSeek } from './deepseekClient';
import { DEFAULT_MODEL } from './config';
import { getTarotSystemPrompt, getAIAssistantPrompt } from './systemPrompts';

interface TarotCard {
  name: string;
  position: "upright" | "reversed";
  meaning: string;
}

// Function for follow-up questions related to tarot readings
export const generateFollowUpResponse = async (
  originalQuestion: string,
  followUpQuestion: string,
  cards: TarotCard[],
  spreadType: string,
  language: 'en' | 'zh' = 'en'
) => {
  try {
    // Format the cards information
    const cardsInfo = cards.map(card => 
      `${card.name} (${card.position === 'upright' ? 'Upright' : 'Reversed'}): ${card.meaning}`
    ).join('\n');

    let prompt;
    if (language === 'zh') {
      prompt = `
        原始问题："${originalQuestion}"
        追问问题："${followUpQuestion}"
        塔罗牌阵类型：${spreadType}
        抽到的塔罗牌：${cards.map(card => `${card.name} (${card.position === 'upright' ? '正位' : '逆位'})`).join('，')}
        
        请基于之前抽的塔罗牌，回答用户的新问题。保持语调支持和积极，避免负面预测。
        不要重复完整的牌阵解读，只需针对新问题给出回应。
        
        注意：请不要使用markdown格式符号，如 ###, **, - 等。
        请使用 ✨ 作为章节开始，使用 🌟 来强调重要概念。`;
    } else {
      prompt = `
        Original question: "${originalQuestion}"
        Follow-up question: "${followUpQuestion}"
        Tarot spread type: ${spreadType}
        Cards drawn: ${cardsInfo}
        
        Based on the tarot cards already drawn, please answer the user's new question.
        Keep the tone supportive and empowering. Avoid negative predictions.
        Don't repeat the entire reading, just focus on addressing the new question.
        
        Note: Please don't use markdown formatting symbols like ###, **, or -. 
        Instead, use ✨ to start new sections and 🌟 to emphasize important concepts.`;
    }

    return await callDeepSeek({
      model: DEFAULT_MODEL,
      messages: [
        {
          role: "system",
          content: getTarotSystemPrompt(language)
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 1000
    }, language);
    
  } catch (error) {
    console.error("Error generating follow-up response:", error);
    return language === 'zh' 
      ? "很抱歉，我目前无法回答这个问题。请稍后再试。" 
      : "I'm sorry, but I couldn't answer your question at this time. Please try again later.";
  }
};

// Regular AI response (non-tarot specific)
export const generateAIResponse = async (
  question: string,
  language: 'en' | 'zh' = 'en'
) => {
  try {
    return await callDeepSeek({
      model: DEFAULT_MODEL,
      messages: [
        {
          role: "system",
          content: getAIAssistantPrompt(language)
        },
        {
          role: "user",
          content: question
        }
      ],
      temperature: 0.7,
      max_tokens: 500
    }, language);
    
  } catch (error) {
    console.error("Error generating AI response:", error);
    return language === 'zh' 
      ? "很抱歉，我目前无法回答这个问题。请稍后再试。" 
      : "I'm sorry, but I couldn't answer your question at this time. Please try again later.";
  }
};

