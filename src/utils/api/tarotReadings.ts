
import { callDeepSeek, callDeepSeekStream } from './deepseekClient';
import { DEFAULT_MODEL } from './config';
import { getTarotSystemPrompt } from './systemPrompts';

interface TarotCard {
  name: string;
  position: "upright" | "reversed";
  meaning: string;
}

// 流式生成塔罗解读
export const generateTarotReadingStream = async (
  question: string,
  cards: TarotCard[],
  spreadType: string,
  language: 'en' | 'zh',
  checkUsageLimit: () => Promise<boolean>,
  incrementQuestionCount: () => Promise<void>,
  onChunk?: (chunk: string) => void
): Promise<string> => {
  try {
    console.log("🔄 Starting streaming tarot reading generation...");

    // 检查使用限制
    const canUse = await checkUsageLimit();
    if (!canUse) {
      console.log("Usage limit reached, returning limit message");
      const limitMessage = language === 'zh'
        ? `🔮 您今天的免费问题次数已用完

✨ 升级到高级会员，享受更多特权：
🌟 每天20次塔罗占卜
💫 更深入的解读分析
🎭 专属会员功能

点击"升级会员"开始您的神秘之旅。`
        : `🔮 You've reached your daily question limit

✨ Upgrade to Premium and enjoy more privileges:
🌟 20 tarot readings per day
💫 Deeper reading analysis
🎭 Exclusive member features

Click "Upgrade" to begin your mystical journey.`;

      // 立即显示限制消息
      if (onChunk) {
        onChunk(limitMessage);
      }
      return limitMessage;
    }

    // 增加问题计数
    await incrementQuestionCount();

    // 构建提示词
    const systemPrompt = getTarotSystemPrompt(language);
    const cardsInfo = cards.map(card => `${card.name} (${card.position})`).join(', ');

    let prompt;
    if (language === 'zh') {
      prompt = `✨ 神秘的塔罗之声召唤着你...

🔮 问询者的问题："${question}"
🌟 选择的牌阵：${spreadType}
💫 显现的卡牌：${cards.map(card => `${card.name}(${card.position === 'upright' ? '正位' : '逆位'})`).join('，')}

请以温和亲切的塔罗咨询师身份，为这位问询者提供一次深入而温暖的解读。

你的解读必须严格按照以下4段式结构，每段内容要丰富详细（至少80-100字）：

🔮 卡牌解析
客观真实地描述每张牌的含义和象征，不要过度美化。分析牌面的实际图像元素，诚实地解释正位或逆位在当前情境中的意义。

🌟 问题解答
温和地回应问询者的具体问题，结合卡牌的指引给出真诚而有帮助的回答。用关怀的语调，提供实际的洞察和方向。

💫 行动指导
以朋友的身份，提供4-6个温暖而实用的建议，帮助问询者在日常生活中找到前进的方向。建议要具体可行，充满关怀。

✨ 精神启示
用温和的语言探讨这次解读对问询者内心成长的意义，给出温暖而有深度的人生思考，像朋友般的陪伴和鼓励。

重要要求：
- 每个部分都要内容丰富，总字数不少于300字
- 语调温和亲切，像关怀的朋友
- 卡牌信息要真实客观，不过度美化
- 严格按照🔮🌟💫✨的顺序和格式
- 绝对禁止使用markdown格式符号（**、*、#、-等）
- 只使用纯文本和emoji，便于快速流式显示`;
    } else {
      prompt = `✨ The mystical voice of tarot calls to you...

🔮 Seeker's Question: "${question}"
🌟 Chosen Spread: ${spreadType}
💫 Revealed Cards: ${cardsInfo}

As a gentle and caring tarot counselor, provide a deep and warm reading for this seeker.

Your reading must strictly follow this 4-section structure, with rich and detailed content in each section (at least 80-100 words each):

🔮 Card Analysis
Objectively and truthfully describe each card's meaning and symbolism without over-beautification. Analyze the actual imagery elements and honestly explain what upright or reversed positions mean in the current context.

🌟 Question Answer
Gently respond to the seeker's specific question, combining the cards' guidance to give sincere and helpful answers. Use a caring tone to provide practical insights and direction.

💫 Action Guidance
As a friend, offer 4-6 specific and actionable suggestions to help the seeker find direction in their daily life. Make suggestions practical and full of care.

✨ Spiritual Insights
Use gentle language to explore what this reading means for the seeker's inner growth, providing warm and thoughtful life reflections with friend-like companionship and encouragement.

Important Requirements:
- Each section must be content-rich, with total word count no less than 300 words
- Tone should be gentle and caring, like a caring friend
- Card information should be truthful and objective, not over-beautified
- Strictly follow the 🔮🌟💫✨ order and format
- Absolutely forbidden to use markdown symbols (**, *, #, -, etc.)
- Only use plain text and emojis for fast streaming display`;
    }

    // 使用流式API调用
    const result = await callDeepSeekStream({
      model: DEFAULT_MODEL,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: prompt }
      ],
      temperature: 0.6,
      max_tokens: 1200
    }, language, onChunk);

    console.log("✅ Streaming tarot reading generation completed");
    return result;

  } catch (error) {
    console.error("❌ Error in streaming tarot reading generation:", error);
    const errorMessage = language === 'zh'
      ? "很抱歉，我目前无法生成解读。请稍后再试。"
      : "I'm sorry, but I couldn't generate a reading at this time. Please try again later.";

    if (onChunk) {
      onChunk(errorMessage);
    }
    return errorMessage;
  }
};

export const generateTarotReading = async (
  question: string,
  cards: TarotCard[],
  spreadType: string,
  language: 'en' | 'zh' = 'en',
  checkUsageLimit?: () => Promise<boolean>,
  incrementQuestionCount?: () => Promise<void>
) => {
  try {
    console.log("=== generateTarotReading called ===");
    console.log("checkUsageLimit:", typeof checkUsageLimit);
    console.log("incrementQuestionCount:", typeof incrementQuestionCount);

    // 检查使用限制
    if (checkUsageLimit) {
      console.log("Checking usage limit...");
      const canUse = await checkUsageLimit();
      console.log("Can use:", canUse);
      if (!canUse) {
        console.log("Usage limit reached, returning limit message");
        return language === 'zh'
          ? `🔮 您今天的免费问题次数已用完

✨ 升级到高级会员，享受更多特权：
🌟 每天20次塔罗占卜
💫 更深入的解读分析
🎭 专属会员功能

点击"升级会员"开始您的神秘之旅。`
          : `🔮 You've reached your daily question limit

✨ Upgrade to Premium and enjoy more privileges:
🌟 20 tarot readings per day
💫 Deeper reading analysis
🎭 Exclusive member features

Click "Upgrade" to begin your mystical journey.`;
      }
    }

    // 立即增加问题计数（在API调用之前）
    if (incrementQuestionCount) {
      console.log("Incrementing question count...");
      await incrementQuestionCount();
      console.log("Question count incremented successfully");
    } else {
      console.log("No incrementQuestionCount function provided");
    }

    // Format the cards information for the prompt
    const cardsInfo = cards.map(card =>
      `${card.name} (${card.position === 'upright' ? 'Upright' : 'Reversed'}): ${card.meaning}`
    ).join('\n');

    // 检测问题语言（更准确的检测）
    const containsChinese = /[\u4e00-\u9fff]/.test(question);
    const actualLanguage = containsChinese ? 'zh' : language;

    console.log(`Question: "${question}"`);
    console.log(`UI Language: ${language}, Detected Language: ${actualLanguage}, Contains Chinese: ${containsChinese}`);

    let prompt;
    if (actualLanguage === 'zh') {
      prompt = `✨ 神秘的塔罗之声召唤着你...

🔮 问询者的问题："${question}"
🌟 选择的牌阵：${spreadType}
💫 显现的卡牌：${cards.map(card => `${card.name}(${card.position === 'upright' ? '正位' : '逆位'})`).join('，')}

请以温和亲切的塔罗咨询师身份，为这位问询者提供一次深入而温暖的解读。

你的解读必须严格按照以下4段式结构，每段内容要丰富详细（至少80-100字）：

🔮 卡牌解析
详细描述每张牌的象征意义、图像元素和在当前情境中的含义。要客观真实，不过度美化，但用温和的语调解释牌面信息。分析正位或逆位的具体意义，以及卡牌之间的相互关系。

🌟 问题解答
温和而直接地回应问询者的具体问题，结合卡牌的指引给出真诚的答案。用关怀的语调提供实际的洞察，帮助问询者理解当前的情况和可能的发展方向。

💫 行动指导
以朋友的身份，提供4-6个具体可行的建议和行动步骤。每个建议都要实用且充满关怀，帮助问询者在日常生活中应用这次解读的智慧。

✨ 精神启示
用温和深刻的语言探讨这次解读对问询者内心成长和人生哲理的意义。提供温暖的鼓励和深层的人生思考，像朋友般的陪伴和支持。

重要要求：
- 每个部分都要内容丰富，总字数不少于300字
- 语调温和亲切，像关怀的朋友
- 卡牌信息要真实客观，不过度美化
- 严格按照🔮🌟💫✨的顺序和格式
- 绝对禁止使用markdown格式符号（**、*、#、-等）
- 只使用纯文本和emoji，便于快速流式显示`;
    } else {
      prompt = `✨ The mystical voice of tarot calls to you...

🔮 Seeker's Question: "${question}"
🌟 Chosen Spread: ${spreadType}
💫 Revealed Cards: ${cardsInfo}

As a gentle and caring tarot counselor, provide a deep and warm reading for this seeker.

Your reading must strictly follow this 4-section structure, with rich and detailed content in each section (at least 80-100 words each):

🔮 Card Analysis
Provide detailed description of each card's symbolic meaning, imagery elements, and significance in the current context. Be objective and truthful without over-beautification, but use a gentle tone to explain the card information. Analyze the specific meaning of upright or reversed positions and the relationships between cards.

🌟 Question Answer
Gently and directly respond to the seeker's specific question, combining the cards' guidance to give sincere answers. Use a caring tone to provide practical insights, helping the seeker understand their current situation and possible directions.

💫 Action Guidance
As a friend, offer 4-6 specific and actionable suggestions and steps. Each suggestion should be practical and full of care, helping the seeker apply this reading's wisdom in their daily life.

✨ Spiritual Insights
Use gentle and profound language to explore what this reading means for the seeker's inner growth and life philosophy. Provide warm encouragement and deep life reflections with friend-like companionship and support.

Important Requirements:
- Each section must be content-rich, with total word count no less than 300 words
- Tone should be gentle and caring, like a caring friend
- Card information should be truthful and objective, not over-beautified
- Strictly follow the 🔮🌟💫✨ order and format
- Absolutely forbidden to use markdown symbols (**, *, #, -, etc.)
- Only use plain text and emojis for fast streaming display`;
    }

    console.log("Sending prompt to DeepSeek API:", prompt);

    const result = await callDeepSeek({
      model: DEFAULT_MODEL,
      messages: [
        {
          role: "system",
          content: getTarotSystemPrompt(actualLanguage)
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.6,
      max_tokens: 1200
    }, actualLanguage);

    return result;

  } catch (error) {
    console.error("Error generating reading:", error);
    return language === 'zh'
      ? "很抱歉，我目前无法生成解读。请稍后再试。"
      : "I'm sorry, but I couldn't generate a reading at this time. Please try again later.";
  }
};

// 🌐 翻译已有解读的功能
export const translateTarotReading = async (
  originalReading: string,
  targetLanguage: 'en' | 'zh',
  originalQuestion?: string,
  cards?: TarotCard[]
): Promise<string> => {
  try {
    console.log("🌐 Translating tarot reading to:", targetLanguage);

    const systemPrompt = targetLanguage === 'zh'
      ? `你是专业翻译师。将英文塔罗解读翻译成自然流畅的中文，保持温暖语调和神秘感。保留emoji和段落结构，禁用markdown符号。直接输出翻译结果，不要添加任何说明。`
      : `You are a professional translator. Translate Chinese tarot reading into natural, fluent English while maintaining warm tone and mystical feeling. Keep emojis and paragraph structure, no markdown symbols. Output translation directly without any explanations.`;

    const translationPrompt = targetLanguage === 'zh'
      ? `${originalReading}`
      : `${originalReading}`;

    const result = await callDeepSeek({
      model: DEFAULT_MODEL,
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: translationPrompt
        }
      ],
      temperature: 0.3, // 较低温度确保翻译准确性
      max_tokens: 1500
    }, targetLanguage);

    console.log("✅ Translation completed");
    return result;

  } catch (error) {
    console.error("❌ Error translating reading:", error);
    return targetLanguage === 'zh'
      ? "很抱歉，翻译功能暂时不可用。请稍后再试。"
      : "Sorry, translation feature is temporarily unavailable. Please try again later.";
  }
};
