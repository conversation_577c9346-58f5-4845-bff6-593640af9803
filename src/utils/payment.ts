import { CREEM_API_KEY, CREEM_API_URL } from './api/config';

export interface PaymentSession {
  id: string;
  url: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
}

export interface PaymentProduct {
  id: string;
  name: string;
  price: number;
  currency: string;
  description: string;
}

// 创建支付会话
export const createPaymentSession = async (
  productId: string,
  userId: string,
  userEmail: string,
  successUrl: string,
  cancelUrl: string
): Promise<PaymentSession | null> => {
  try {
    // 根据Creem.io官方文档的标准格式
    const requestBody = {
      product_id: productId,
      request_id: userId,
      success_url: successUrl,
      cancel_url: cancelUrl,
      customer: {
        email: userEmail
      },
      metadata: {
        userId: userId,
        userEmail: userEmail,
        subscriptionType: 'monthly',
        platform: 'mystic-card-mirror-vip'
      }
    };

    const response = await fetch(`https://api.creem.io/v1/checkouts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': CREEM_API_KEY
      },
      body: JSON.stringify(requestBody)
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Creem.io API error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });

      // 如果API调用失败，回退到直接产品链接
      console.log('API failed, falling back to direct product link...');
      const fallbackUrl = `https://www.creem.io/test/payment/${productId}?success_url=${encodeURIComponent(successUrl)}&cancel_url=${encodeURIComponent(cancelUrl)}&customer_email=${encodeURIComponent(userEmail)}&customer_id=${userId}`;

      return {
        id: 'fallback',
        url: fallbackUrl,
        status: 'pending'
      };
    }

    const data = await response.json();

    return {
      id: data.id,
      url: data.checkout_url || data.url, // 根据文档，可能返回checkout_url
      status: 'pending'
    };
  } catch (error) {
    console.error('Error creating payment session:', error);

    // 网络错误时的回退方案
    console.log('Network error, using fallback payment URL...');
    const fallbackUrl = `https://www.creem.io/test/payment/${productId}?success_url=${encodeURIComponent(successUrl)}&cancel_url=${encodeURIComponent(cancelUrl)}&customer_email=${encodeURIComponent(userEmail)}&customer_id=${userId}`;

    return {
      id: 'fallback',
      url: fallbackUrl,
      status: 'pending'
    };
  }
};

// 验证支付状态
export const verifyPaymentStatus = async (sessionId: string): Promise<string | null> => {
  try {
    const response = await fetch(`${CREEM_API_URL}/checkout/sessions/${sessionId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${CREEM_API_KEY}`
      }
    });

    if (!response.ok) {
      console.error('Failed to verify payment status');
      return null;
    }

    const data = await response.json();
    return data.status;
  } catch (error) {
    console.error('Error verifying payment status:', error);
    return null;
  }
};

// 处理Webhook事件
export const handleWebhookEvent = async (event: any) => {
  try {
    switch (event.type) {
      case 'checkout.session.completed':
        // 支付成功，激活用户订阅
        await activateSubscription(event.data.object);
        break;
      case 'checkout.session.failed':
        // 支付失败，记录日志
        console.error('Payment failed:', event.data.object);
        break;
      case 'subscription.cancelled':
        // 订阅取消，更新用户状态
        await cancelSubscription(event.data.object);
        break;
      default:
        console.log('Unhandled webhook event:', event.type);
    }
  } catch (error) {
    console.error('Error handling webhook event:', error);
  }
};

// 激活订阅
const activateSubscription = async (sessionData: any) => {
  try {
    const userId = sessionData.metadata?.user_id;
    if (!userId) {
      console.error('No user ID found in session metadata');
      return;
    }

    // 这里需要调用Supabase来更新用户订阅状态
    // 由于我们在前端，这个函数应该在后端实现
    console.log('Activating subscription for user:', userId);

    // 前端可以通过监听URL参数或localStorage来处理支付成功
    if (typeof window !== 'undefined') {
      localStorage.setItem('payment_success', 'true');
      localStorage.setItem('session_id', sessionData.id);
    }
  } catch (error) {
    console.error('Error activating subscription:', error);
  }
};

// 取消订阅
const cancelSubscription = async (subscriptionData: any) => {
  try {
    const userId = subscriptionData.metadata?.user_id;
    if (!userId) {
      console.error('No user ID found in subscription metadata');
      return;
    }

    console.log('Cancelling subscription for user:', userId);

    // 前端处理取消订阅
    if (typeof window !== 'undefined') {
      localStorage.setItem('subscription_cancelled', 'true');
    }
  } catch (error) {
    console.error('Error cancelling subscription:', error);
  }
};

// 获取产品信息
export const getProducts = (): PaymentProduct[] => {
  return [
    {
      id: 'prod_2Ei8CdkAewpPbGZFz09Cgq', // Creem.io测试产品ID
      name: 'Monthly Premium Subscription',
      price: 4.99,
      currency: 'USD',
      description: 'Unlimited tarot readings and premium features'
    }
  ];
};

// 防重复调用的标志
let paymentInProgress = false;

// 启动支付流程
export const initiatePayment = async (
  productId: string,
  userId: string,
  userEmail: string
): Promise<boolean> => {
  // 防止重复调用
  if (paymentInProgress) {
    console.log('⚠️ Payment already in progress, skipping duplicate call');
    return false;
  }

  try {
    paymentInProgress = true;
    console.log('🔒 Setting payment in progress flag');

    const successUrl = `https://mystic-card-mirror-vip.lovable.app/payment-success?user_id=${userId}&request_id=${userId}`;
    const cancelUrl = `https://mystic-card-mirror-vip.lovable.app/payment-cancel?user_id=${userId}`;

    // 创建checkout session
    const session = await createPaymentSession(
      productId,
      userId,
      userEmail,
      successUrl,
      cancelUrl
    );

    if (session && session.url) {
      console.log('✅ Payment session created, redirecting to:', session.url);

      // 使用setTimeout确保状态更新后再跳转
      setTimeout(() => {
        window.location.href = session.url;
      }, 100);

      return true;
    }

    console.error('❌ No payment URL received from session');
    return false;
  } catch (error) {
    console.error('❌ Error initiating payment:', error);
    return false;
  } finally {
    // 在一定时间后重置标志，以防页面没有跳转
    setTimeout(() => {
      paymentInProgress = false;
      console.log('🔓 Reset payment in progress flag');
    }, 5000);
  }
};

// 检查支付成功状态
export const checkPaymentSuccess = (): boolean => {
  if (typeof window === 'undefined') return false;

  const paymentSuccess = localStorage.getItem('payment_success');
  if (paymentSuccess === 'true') {
    localStorage.removeItem('payment_success');
    return true;
  }

  return false;
};

// 检查订阅取消状态
export const checkSubscriptionCancelled = (): boolean => {
  if (typeof window === 'undefined') return false;

  const subscriptionCancelled = localStorage.getItem('subscription_cancelled');
  if (subscriptionCancelled === 'true') {
    localStorage.removeItem('subscription_cancelled');
    return true;
  }

  return false;
};

// Creem.io 回调参数接口
export interface CreemRedirectParams {
  request_id?: string | null;
  checkout_id?: string | null;
  order_id?: string | null;
  customer_id?: string | null;
  subscription_id?: string | null;
  product_id?: string | null;
  user_id?: string | null;
  signature?: string | null;
}

// 验证Creem签名
export const verifyCreemSignature = (params: CreemRedirectParams): boolean => {
  try {
    const { signature, ...otherParams } = params;

    if (!signature) {
      console.log('❌ No signature provided');
      return false;
    }

    // 构建签名数据
    const data = Object.entries(otherParams)
      .filter(([_, value]) => value !== null && value !== undefined)
      .map(([key, value]) => `${key}=${value}`)
      .concat(`salt=${CREEM_API_KEY}`)
      .join('|');

    console.log('🔐 Signature verification data:', data);

    // 在浏览器环境中，我们无法直接使用crypto.createHash
    // 所以我们先记录数据，实际验证在服务端进行
    console.log('🔐 Expected signature format:', data);
    console.log('🔐 Received signature:', signature);

    // 暂时返回true，实际项目中应该在服务端验证
    return true;
  } catch (error) {
    console.error('❌ Error verifying signature:', error);
    return false;
  }
};

// 解析URL参数为Creem回调参数
export const parseCreemCallback = (searchParams: URLSearchParams): CreemRedirectParams => {
  return {
    request_id: searchParams.get('request_id'),
    checkout_id: searchParams.get('checkout_id'),
    order_id: searchParams.get('order_id'),
    customer_id: searchParams.get('customer_id'),
    subscription_id: searchParams.get('subscription_id'),
    product_id: searchParams.get('product_id'),
    user_id: searchParams.get('user_id'),
    signature: searchParams.get('signature')
  };
};

// 获取Creem.io Customer Portal URL用于订阅管理
export const getCreemCustomerPortalUrl = async (customerId: string): Promise<{ success: boolean; portalUrl?: string; error?: string }> => {
  try {
    console.log('🔄 Getting Creem Customer Portal URL for:', customerId);

    // 调用我们的Netlify函数代理
    const response = await fetch('/.netlify/functions/cancel-subscription', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        customerId: customerId
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('❌ Failed to get Customer Portal URL:', response.status, errorData);
      return {
        success: false,
        error: `Proxy error: ${response.status} ${errorData.error || 'Unknown error'}`
      };
    }

    const data = await response.json();

    if (!data.success) {
      console.error('❌ Failed to get Customer Portal URL:', data.error);
      return {
        success: false,
        error: data.error || 'Failed to get portal URL'
      };
    }

    console.log('✅ Creem Customer Portal URL retrieved successfully:', data.portalUrl);
    return {
      success: true,
      portalUrl: data.portalUrl
    };

  } catch (error) {
    console.error('❌ Error getting Customer Portal URL:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error'
    };
  }
};

// 通过后端代理生成Customer Portal URL（避免CORS问题）
export const generateCustomerPortalUrl = async (customerId: string): Promise<string | null> => {
  try {
    console.log('🔗 Generating Customer Portal URL for customer:', customerId);

    // 使用我们的Netlify函数代理API调用
    const response = await fetch('/.netlify/functions/customer-portal', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        customer_id: customerId
      })
    });

    console.log('📡 Response status:', response.status, response.statusText);

    if (!response.ok) {
      // 尝试获取响应文本以调试
      const responseText = await response.text();
      console.error('❌ Failed to generate Customer Portal URL:', {
        status: response.status,
        statusText: response.statusText,
        responseText: responseText.substring(0, 200) // 只显示前200个字符
      });

      // 如果返回的是HTML（404页面），说明函数路径有问题
      if (responseText.includes('<!DOCTYPE')) {
        console.error('❌ Function not found - received HTML instead of JSON');
        throw new Error('Customer Portal function not deployed or path incorrect');
      }

      return null;
    }

    const data = await response.json();

    if (!data.success || !data.customer_portal_link) {
      console.error('Invalid response from customer portal API:', data);
      return null;
    }

    console.log('✅ Customer Portal URL generated:', data.customer_portal_link);
    return data.customer_portal_link;
  } catch (error) {
    console.error('Error generating Customer Portal URL:', error);
    return null;
  }
};

// 保留原函数名以兼容现有代码，但现在重定向到Customer Portal
export const cancelCreemSubscription = async (subscriptionId: string): Promise<{ success: boolean; error?: string }> => {
  // 注意：我们需要customer_id而不是subscription_id来获取Portal URL
  // 这个函数现在需要从数据库获取customer_id
  console.log('⚠️ cancelCreemSubscription called with subscription_id:', subscriptionId);
  console.log('⚠️ This function now requires customer_id. Please use getCreemCustomerPortalUrl() instead.');

  return {
    success: false,
    error: 'This function has been deprecated. Please use the Customer Portal approach.'
  };
};
