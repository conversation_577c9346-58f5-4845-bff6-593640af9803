// 测试支付API连接
import { CREEM_API_KEY } from './api/config';

export const testCreemAPI = async () => {
  try {
    console.log('Testing Creem.io API connection...');
    console.log('API Key:', CREEM_API_KEY ? 'Present' : 'Missing');
    
    // 测试API连接
    const response = await fetch('https://api.creem.io/v1/checkouts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': CREEM_API_KEY
      },
      body: JSON.stringify({
        product_id: 'prod_2Ei8CdkAewpPbGZFz09Cgq',
        success_url: 'https://mystic-card-mirror-vip.lovable.app/payment/success',
        cancel_url: 'https://mystic-card-mirror-vip.lovable.app/payment/cancel',
        customer_email: '<EMAIL>',
        request_id: 'test_user_123'
      })
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      return false;
    }

    const data = await response.json();
    console.log('API Success:', data);
    return true;

  } catch (error) {
    console.error('Network Error:', error);
    return false;
  }
};

// 简化的支付测试
export const testSimplePayment = async (userEmail: string, userId: string) => {
  try {
    console.log('Testing simple payment creation...');
    
    const requestBody = {
      product_id: 'prod_2Ei8CdkAewpPbGZFz09Cgq',
      success_url: 'https://mystic-card-mirror-vip.lovable.app/payment/success',
      cancel_url: 'https://mystic-card-mirror-vip.lovable.app/payment/cancel',
      customer_email: userEmail,
      request_id: userId
    };

    console.log('Request body:', requestBody);

    const response = await fetch('https://api.creem.io/v1/checkouts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': CREEM_API_KEY
      },
      body: JSON.stringify(requestBody)
    });

    console.log('Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response:', errorText);
      
      // 尝试解析错误信息
      try {
        const errorJson = JSON.parse(errorText);
        console.error('Parsed error:', errorJson);
      } catch (e) {
        console.error('Could not parse error as JSON');
      }
      
      return null;
    }

    const data = await response.json();
    console.log('Success response:', data);
    return data;

  } catch (error) {
    console.error('Test payment error:', error);
    return null;
  }
};

// 检查API密钥格式
export const validateAPIKey = () => {
  console.log('Validating API key...');
  console.log('API Key length:', CREEM_API_KEY?.length || 0);
  console.log('API Key starts with creem_:', CREEM_API_KEY?.startsWith('creem_') || false);
  console.log('API Key format valid:', /^creem_[a-zA-Z0-9]+$/.test(CREEM_API_KEY || ''));
  
  return {
    present: !!CREEM_API_KEY,
    length: CREEM_API_KEY?.length || 0,
    startsWithCreem: CREEM_API_KEY?.startsWith('creem_') || false,
    formatValid: /^creem_[a-zA-Z0-9]+$/.test(CREEM_API_KEY || '')
  };
};
