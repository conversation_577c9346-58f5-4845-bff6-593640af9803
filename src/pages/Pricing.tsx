import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import { Check, Star, Crown, Zap } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { initiatePayment } from '@/utils/payment';


const Pricing = () => {
  const { language } = useLanguage();
  const { user } = useAuth();
  const { subscription, isTrialActive, isSubscriptionActive, startFreeTrial, daysRemainingInTrial, refreshSubscription } = useSubscription();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  // 🔧 移除自动试用逻辑，所有升级都通过Creem付费



  const handleUpgrade = async () => {
    // 防止重复点击
    if (loading) {
      console.log('⚠️ Payment already in progress, ignoring click');
      return;
    }

    if (!user) {
      navigate('/auth');
      return;
    }

    setLoading(true);

    try {
      // 🔧 新逻辑：跳转到Creem付费页面购买一次性月度订阅
      console.log('💳 Redirecting to Creem payment page for one-time monthly subscription...');

      toast({
        title: language === 'en' ? 'Redirecting to Payment' : '正在跳转到支付页面',
        description: language === 'en'
          ? 'Purchase your one-time monthly subscription...'
          : '购买您的一次性月度订阅...'
      });

      const success = await initiatePayment(
        'prod_2Ei8CdkAewpPbGZFz09Cgq',
        user.id,
        user.email || ''
      );

      if (!success) {
        throw new Error('Payment initiation failed');
      }

    } catch (error) {
      console.error('❌ Error in handleUpgrade:', error);
      toast({
        title: language === 'en' ? 'Error' : '错误',
        description: language === 'en'
          ? `Error: ${error.message}`
          : `错误：${error.message}`,
        variant: 'destructive'
      });
      setLoading(false);
    }
  };

  const features = {
    free: [
      language === 'en' ? '1 question per day' : '每天1个问题',
      language === 'en' ? 'Basic tarot readings' : '基础塔罗占卜',
      language === 'en' ? 'Standard AI responses' : '标准AI回复'
    ],
    trial: [
      language === 'en' ? '20 questions per day for 3 days' : '3天内每天20个问题',
      language === 'en' ? 'Premium tarot readings' : '高级塔罗占卜',
      language === 'en' ? 'Advanced AI insights' : '高级AI洞察',
      language === 'en' ? 'Priority support' : '优先支持'
    ],
    premium: [
      language === 'en' ? '20 questions per day' : '每天20个问题',
      language === 'en' ? 'Premium tarot readings' : '高级塔罗占卜',
      language === 'en' ? 'Advanced AI insights' : '高级AI洞察',
      language === 'en' ? 'Priority support' : '优先支持',
      language === 'en' ? 'Reading history' : '占卜历史记录',
      language === 'en' ? 'Exclusive spreads' : '独家牌阵'
    ]
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 py-12 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <motion.h1
            className="text-4xl md:text-5xl font-bold text-white mb-4"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {language === 'en' ? 'Choose Your Plan' : '选择您的套餐'}
          </motion.h1>
          <motion.p
            className="text-xl text-purple-200 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {language === 'en'
              ? 'Experience our transparent one-time monthly subscription model with no automatic recurring charges. You have complete control over your spiritual journey.'
              : '体验我们透明的一次性月度订阅模式，无自动循环收费。您完全掌控自己的精神之旅。'
            }
          </motion.p>
        </div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-2 gap-8 mb-12 max-w-4xl mx-auto">
          {/* Free Plan */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card className="relative bg-white/10 backdrop-blur-lg border-white/20 text-white h-full">
              <CardHeader className="text-center">
                <div className="flex justify-center mb-4">
                  <Star className="h-12 w-12 text-purple-300" />
                </div>
                <CardTitle className="text-2xl mb-2">
                  {language === 'en' ? 'Free' : '免费'}
                </CardTitle>
                <div className="text-3xl font-bold">
                  $0<span className="text-lg font-normal">/month</span>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-6">
                  {features.free.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <Check className="h-5 w-5 text-green-400 mr-3" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
                <Button
                  variant="outline"
                  className="w-full border-white/30 text-white hover:bg-white/10"
                  disabled={user && !isTrialActive && !isSubscriptionActive}
                >
                  {language === 'en' ? 'Current Plan' : '当前套餐'}
                </Button>
              </CardContent>
            </Card>
          </motion.div>



          {/* Premium Plan */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.15 }}
          >
            <Card className="relative bg-gradient-to-br from-yellow-600/20 to-orange-600/20 backdrop-blur-lg border-yellow-300/30 text-white h-full">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1">
                  {language === 'en' ? 'Most Popular' : '最受欢迎'}
                </Badge>
              </div>
              <CardHeader className="text-center pt-8">
                <div className="flex justify-center mb-4">
                  <Crown className="h-12 w-12 text-yellow-400" />
                </div>
                <CardTitle className="text-2xl mb-2">
                  {language === 'en' ? 'Premium' : '高级会员'}
                </CardTitle>
                <div className="text-3xl font-bold">
                  $4.99<span className="text-lg font-normal">/month</span>
                </div>
                <div className="mt-2 p-2 bg-blue-500/20 rounded-lg border border-blue-400/30">
                  <div className="text-sm font-semibold text-blue-300">
                    💳 {language === 'en' ? 'One-Time Payment' : '一次性付费'}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-6">
                  {features.premium.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <Check className="h-5 w-5 text-green-400 mr-3" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
                {isSubscriptionActive ? (
                  <Button className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600" disabled>
                    {language === 'en' ? 'Current Plan' : '当前套餐'}
                  </Button>
                ) : (
                  <div className="space-y-3">
                    {isTrialActive && (
                      <div className="text-center p-2 bg-green-500/20 rounded-lg border border-green-400/30">
                        <div className="text-sm font-semibold text-green-300">
                          {language === 'en'
                            ? `✨ Free Trial Active (${daysRemainingInTrial} days left)`
                            : `✨ 免费试用中 (剩余${daysRemainingInTrial}天)`
                          }
                        </div>
                      </div>
                    )}
                    <Button
                      className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 disabled:opacity-50 disabled:cursor-not-allowed"
                      onClick={handleUpgrade}
                      disabled={loading}
                    >
                      {loading ? (
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          {language === 'en' ? 'Redirecting to Payment...' : '正在跳转到支付页面...'}
                        </div>
                      ) : isTrialActive ? (
                        language === 'en' ? 'Upgrade to Monthly Subscription' : '升级到月度订阅'
                      ) : (
                        language === 'en' ? 'Buy Monthly Subscription' : '购买月度订阅'
                      )}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>


        </div>

        {/* FAQ Section */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <h2 className="text-2xl font-bold text-white mb-6">
            {language === 'en' ? 'Frequently Asked Questions' : '常见问题'}
          </h2>
          <div className="grid md:grid-cols-2 gap-6 text-left">
            <Card className="bg-white/10 backdrop-blur-lg border-white/20 text-white">
              <CardContent className="p-6">
                <h3 className="font-semibold mb-2">
                  {language === 'en' ? 'Do new users get a free trial?' : '新用户有免费试用吗？'}
                </h3>
                <p className="text-purple-200">
                  {language === 'en'
                    ? 'Yes! New users automatically receive a 3-day premium experience upon registration. After 3 days, your account switches to the free plan (1 question per day).'
                    : '有的！新用户注册后自动获得3天高级体验。3天后，您的账户将切换到免费套餐（每天1次提问）。'
                  }
                </p>
              </CardContent>
            </Card>
            <Card className="bg-white/10 backdrop-blur-lg border-white/20 text-white">
              <CardContent className="p-6">
                <h3 className="font-semibold mb-2">
                  {language === 'en' ? 'How does one-time payment work?' : '一次性付费如何运作？'}
                </h3>
                <p className="text-purple-200">
                  {language === 'en'
                    ? 'Each payment gives you exactly one month of premium access. No automatic renewals, complete transparency, and full control over your subscription.'
                    : '每次付费为您提供整整一个月的高级访问权限。无自动续费，完全透明，您完全掌控订阅。'
                  }
                </p>
              </CardContent>
            </Card>
            <Card className="bg-white/10 backdrop-blur-lg border-white/20 text-white">
              <CardContent className="p-6">
                <h3 className="font-semibold mb-2">
                  {language === 'en' ? 'What payment methods do you accept?' : '接受哪些支付方式？'}
                </h3>
                <p className="text-purple-200">
                  {language === 'en'
                    ? 'We accept all major credit cards (Visa, Mastercard, American Express) and Google Pay for secure and convenient payments.'
                    : '我们接受所有主要信用卡（Visa、Mastercard、美国运通）和Google Pay，提供安全便捷的支付体验。'
                  }
                </p>
              </CardContent>
            </Card>
            <Card className="bg-white/10 backdrop-blur-lg border-white/20 text-white">
              <CardContent className="p-6">
                <h3 className="font-semibold mb-2">
                  {language === 'en' ? 'What happens when my subscription expires?' : '订阅到期后会发生什么？'}
                </h3>
                <p className="text-purple-200">
                  {language === 'en'
                    ? 'Your account automatically switches to the free plan (1 question per day). You can resubscribe anytime to continue enjoying premium features.'
                    : '您的账户会自动切换到免费套餐（每天1次提问）。您可以随时重新订阅以继续享受高级功能。'
                  }
                </p>
              </CardContent>
            </Card>
            <Card className="bg-white/10 backdrop-blur-lg border-white/20 text-white">
              <CardContent className="p-6">
                <h3 className="font-semibold mb-2">
                  {language === 'en' ? 'Is my payment information secure?' : '我的支付信息安全吗？'}
                </h3>
                <p className="text-purple-200">
                  {language === 'en'
                    ? 'Yes, all payments are securely processed through Creem.io with industry-standard encryption and PCI DSS compliance.'
                    : '是的，所有支付都通过Creem.io安全处理，采用行业标准加密和PCI DSS合规性。'
                  }
                </p>
              </CardContent>
            </Card>
          </div>
        </motion.div>

        {/* Back to Home */}
        <div className="text-center mt-8">
          <Link to="/">
            <Button variant="outline" className="border-white/30 text-white hover:bg-white/10">
              {language === 'en' ? 'Back to Home' : '返回首页'}
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Pricing;
