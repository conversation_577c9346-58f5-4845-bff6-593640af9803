
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { User, Trash2, Star, Eye, Crown, Calendar } from 'lucide-react';
import {
  Di<PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>rigger,
} from '@/components/ui/dialog';
import FeedbackDialog from '@/components/FeedbackDialog';

interface TarotReading {
  id: string;
  question: string;
  spread_type: string;
  ai_reading: string;
  created_at: string;
  cards: any; // JSON data for the cards
  feedback?: {
    accuracy_rating?: number;
    satisfaction_rating?: number;
    usefulness_rating?: number;
    feedback_text?: string;
  };
}

const Profile = () => {
  const { user, deleteAccount, signOut } = useAuth();
  const { language } = useLanguage();
  const { subscription, isSubscriptionActive, isTrialActive } = useSubscription();
  const [readings, setReadings] = useState<TarotReading[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedReading, setSelectedReading] = useState<TarotReading | null>(null);
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);

  useEffect(() => {
    if (user) {
      fetchReadings();
    }
  }, [user]);

  const fetchReadings = async () => {
    try {
      console.log("📖 Fetching readings from reading_history table...");
      const { data: readingsData, error } = await supabase
        .from('reading_history')
        .select(`
          id,
          question,
          spread_type,
          ai_reading,
          created_at,
          cards
        `)
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const formattedReadings = readingsData || [];

      setReadings(formattedReadings);
    } catch (error) {
      console.error('Error fetching readings:', error);
      toast({
        title: language === 'en' ? 'Error' : '错误',
        description: language === 'en' ? 'Failed to load readings' : '加载占卜记录失败',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteReading = async (readingId: string) => {
    try {
      console.log("🗑️ Deleting reading from reading_history table:", readingId);
      const { error } = await supabase
        .from('reading_history')
        .delete()
        .eq('id', readingId);

      if (error) throw error;

      setReadings(readings.filter(r => r.id !== readingId));
      toast({
        title: language === 'en' ? 'Success' : '成功',
        description: language === 'en' ? 'Reading deleted' : '占卜记录已删除'
      });
    } catch (error) {
      toast({
        title: language === 'en' ? 'Error' : '错误',
        description: language === 'en' ? 'Failed to delete reading' : '删除占卜记录失败',
        variant: 'destructive'
      });
    }
  };

  const handleDeleteAccount = async () => {
    try {
      const { error } = await deleteAccount();
      if (error) throw error;

      toast({
        title: language === 'en' ? '✅ Account Deleted Successfully' : '✅ 账户删除成功',
        description: language === 'en' ? 'Your account has been permanently deleted. Redirecting to homepage...' : '您的账户已永久删除。正在跳转到首页...'
      });

      // 3秒后跳转到首页
      setTimeout(() => {
        window.location.href = '/';
      }, 3000);

    } catch (error) {
      console.error('❌ Account deletion error:', error);
      toast({
        title: language === 'en' ? 'Error' : '错误',
        description: language === 'en' ? 'Failed to delete account. Please try again.' : '删除账户失败，请重试。',
        variant: 'destructive'
      });
    }
  };

  // 🔧 移除取消订阅功能，采用一次性付费模式

  const renderStars = (rating?: number) => {
    if (!rating) return null;
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map(star => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-mystic-purple">
          {language === 'en' ? 'Loading...' : '加载中...'}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background pt-24 pb-8">
      <div className="container mx-auto px-4 max-w-4xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* 用户信息卡片 */}
          <Card className="mb-8 bg-card border-mystic-purple/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-mystic-purple">
                <User className="h-5 w-5" />
                {language === 'en' ? 'Profile' : '个人资料'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-muted-foreground">
                    {language === 'en' ? 'Email' : '邮箱'}
                  </p>
                  <p className="font-medium">{user?.email}</p>
                </div>

                <div className="flex gap-4">
                  <Button
                    onClick={signOut}
                    variant="outline"
                    className="border-mystic-purple/30 hover:bg-mystic-purple/10"
                  >
                    {language === 'en' ? 'Sign Out' : '退出登录'}
                  </Button>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive">
                        {language === 'en' ? 'Delete Account' : '删除账户'}
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent className="max-w-md">
                      <AlertDialogHeader>
                        <AlertDialogTitle className="text-red-600">
                          {language === 'en' ? '⚠️ Permanently Delete Account' : '⚠️ 永久删除账户'}
                        </AlertDialogTitle>
                        <AlertDialogDescription className="space-y-3">
                          <div className="text-red-700 font-semibold">
                            {language === 'en'
                              ? '🚨 WARNING: This action is IRREVERSIBLE!'
                              : '🚨 警告：此操作不可逆转！'
                            }
                          </div>

                          <div className="text-sm space-y-2">
                            <p>
                              {language === 'en'
                                ? 'Deleting your account will:'
                                : '删除您的账户将：'
                              }
                            </p>
                            <ul className="list-disc list-inside space-y-1 text-xs">
                              <li>
                                {language === 'en'
                                  ? 'Permanently delete your email and password'
                                  : '永久删除您的邮箱和密码'
                                }
                              </li>
                              <li>
                                {language === 'en'
                                  ? 'Remove ALL tarot reading history'
                                  : '删除所有塔罗占卜历史记录'
                                }
                              </li>
                              <li>
                                {language === 'en'
                                  ? 'Cancel subscription (NO REFUNDS)'
                                  : '取消订阅（不予退款）'
                                }
                              </li>

                            </ul>
                          </div>

                          <div className="bg-red-50 p-3 rounded border border-red-200">
                            <p className="text-red-800 text-xs font-medium">
                              {language === 'en'
                                ? '💡 You CANNOT recover your account or data after deletion. Please consider carefully!'
                                : '💡 删除后您无法恢复账户或数据。请慎重考虑！'
                              }
                            </p>
                          </div>
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>
                          {language === 'en' ? 'Cancel' : '取消'}
                        </AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleDeleteAccount}
                          className="bg-red-600 hover:bg-red-700 text-white"
                        >
                          {language === 'en' ? 'Yes, Delete Forever' : '是的，永久删除'}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 订阅信息 */}
          <Card className="mb-8 bg-card border-mystic-purple/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-mystic-purple">
                <Crown className="h-5 w-5" />
                {language === 'en' ? 'Subscription Information' : '订阅信息'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* 订阅状态 */}
                <div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {language === 'en' ? 'Current Plan' : '当前套餐'}
                  </p>
                  <div className="flex items-center gap-2">
                    {isSubscriptionActive ? (
                      <div className="flex items-center gap-2">
                        <Crown className="h-4 w-4 text-yellow-500" />
                        <span className="font-medium text-yellow-600">
                          {language === 'en' ? 'Premium Monthly' : '高级月度会员'}
                        </span>
                      </div>
                    ) : isTrialActive ? (
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-blue-500" />
                        <span className="font-medium text-blue-600">
                          {language === 'en' ? 'Free Trial' : '免费试用'}
                        </span>
                      </div>
                    ) : (
                      <span className="font-medium text-muted-foreground">
                        {language === 'en' ? 'Free Plan' : '免费套餐'}
                      </span>
                    )}
                  </div>
                </div>

                {/* 订阅详情 */}
                {subscription && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">
                        {language === 'en' ? 'Daily Questions Limit' : '每日提问限制'}
                      </p>
                      <p className="font-medium">{subscription.daily_questions_limit}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">
                        {language === 'en' ? 'Questions Used Today' : '今日已使用'}
                      </p>
                      <p className="font-medium">{subscription.daily_questions_used}</p>
                    </div>
                    {subscription.subscription_end_date && (
                      <div className="md:col-span-2">
                        <p className="text-muted-foreground">
                          {language === 'en' ? 'Membership Expires' : '会员到期时间'}
                        </p>
                        <p className="font-medium text-lg">
                          {new Date(subscription.subscription_end_date).toLocaleDateString(
                            language === 'en' ? 'en-US' : 'zh-CN',
                            {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            }
                          )}
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* 一次性付费说明 */}
                {(isSubscriptionActive || isTrialActive) && (
                  <div className="pt-4 border-t border-mystic-purple/20">
                    <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                      <h4 className="font-semibold text-blue-800 mb-2 flex items-center">
                        <span className="mr-2">💡</span>
                        {language === 'en' ? 'One-Time Payment Model' : '一次性付费模式'}
                      </h4>
                      <p className="text-blue-700 text-sm leading-relaxed">
                        {language === 'en'
                          ? 'To optimize user experience, our platform employs a one-time monthly subscription model with no automatic recurring charges. This ensures transparency and gives you complete control over your subscription. If you are satisfied with our service, we warmly welcome you to continue your subscription next month.'
                          : '为了优化用户体验，我们的平台采用一次性月度订阅模式，不会自动循环收费。这确保了透明度，让您完全掌控自己的订阅。如果您对我们的服务满意，我们热忱欢迎您下个月继续订阅。'
                        }
                      </p>
                    </div>
                  </div>
                )}

                {/* 免费用户升级提示 */}
                {subscription?.subscription_type === 'free' && (
                  <div className="pt-4 border-t border-mystic-purple/20">
                    <Link to="/pricing">
                      <Button className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600">
                        {language === 'en' ? 'Upgrade to Premium' : '升级到高级版'}
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 占卜历史记录 */}
          <Card className="bg-card border-mystic-purple/20">
            <CardHeader>
              <CardTitle className="text-mystic-purple">
                {language === 'en' ? 'Tarot Reading History' : '占卜历史记录'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {readings.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">
                  {language === 'en' ? 'No readings yet' : '暂无占卜记录'}
                </p>
              ) : (
                <div className="space-y-4">
                  {readings.map((reading) => (
                    <div
                      key={reading.id}
                      className="border border-mystic-purple/20 rounded-lg p-4 space-y-3"
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h3 className="font-medium text-mystic-purple">
                            {reading.question}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            {reading.spread_type} • {new Date(reading.created_at).toLocaleDateString()}
                          </p>
                        </div>

                        <div className="flex gap-2">
                          {/* View Details Button */}
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button variant="ghost" size="sm" className="text-mystic-purple hover:text-mystic-purple/80">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                              <DialogHeader>
                                <DialogTitle className="text-mystic-purple">
                                  {language === 'en' ? 'Reading Details' : '占卜详情'}
                                </DialogTitle>
                              </DialogHeader>
                              <div className="space-y-6">
                                {/* Question */}
                                <div>
                                  <h3 className="font-medium text-mystic-purple mb-2">
                                    {language === 'en' ? 'Question' : '问题'}
                                  </h3>
                                  <p className="text-lg">{reading.question}</p>
                                </div>

                                {/* Cards */}
                                {reading.cards && (
                                  <div>
                                    <h3 className="font-medium text-mystic-purple mb-2">
                                      {language === 'en' ? 'Cards Drawn' : '抽取的卡牌'}
                                    </h3>
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                      {Array.isArray(reading.cards) ? reading.cards.map((card: any, index: number) => (
                                        <div key={index} className="bg-mystic-dark/20 rounded-lg p-4">
                                          <h4 className="font-medium text-mystic-purple">{card.name}</h4>
                                          <p className="text-sm text-muted-foreground">
                                            {card.position === 'upright' ?
                                              (language === 'en' ? 'Upright' : '正位') :
                                              (language === 'en' ? 'Reversed' : '逆位')
                                            }
                                          </p>
                                          <p className="text-sm mt-2">{card.meaning}</p>
                                        </div>
                                      )) : null}
                                    </div>
                                  </div>
                                )}

                                {/* AI Reading */}
                                <div>
                                  <h3 className="font-medium text-mystic-purple mb-2">
                                    {language === 'en' ? 'Reading' : '解读'}
                                  </h3>
                                  <div className="bg-mystic-dark/20 rounded-lg p-4">
                                    <p className="whitespace-pre-wrap">{reading.ai_reading}</p>
                                  </div>
                                </div>

                                {/* Rate Button */}
                                <div className="flex justify-center">
                                  <Button
                                    onClick={() => {
                                      setSelectedReading(reading);
                                      setFeedbackDialogOpen(true);
                                    }}
                                    className="bg-gradient-to-r from-mystic-purple to-mystic-pink hover:from-mystic-purple/80 hover:to-mystic-pink/80"
                                  >
                                    <Star className="h-4 w-4 mr-2" />
                                    {language === 'en' ? 'Rate This Reading' : '评价这次占卜'}
                                  </Button>
                                </div>
                              </div>
                            </DialogContent>
                          </Dialog>

                          {/* Delete Button */}
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm" className="text-red-500 hover:text-red-700">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>
                                  {language === 'en' ? 'Delete Reading' : '删除占卜记录'}
                                </AlertDialogTitle>
                                <AlertDialogDescription>
                                  {language === 'en'
                                    ? 'Are you sure you want to delete this reading?'
                                    : '确定要删除这个占卜记录吗？'
                                  }
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>
                                  {language === 'en' ? 'Cancel' : '取消'}
                                </AlertDialogCancel>
                                <AlertDialogAction onClick={() => handleDeleteReading(reading.id)}>
                                  {language === 'en' ? 'Delete' : '删除'}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </div>

                      <div className="text-sm">
                        <p className="line-clamp-3">{reading.ai_reading}</p>
                      </div>

                      {reading.feedback && (
                        <div className="bg-mystic-dark/20 rounded p-3 space-y-2">
                          <h4 className="text-sm font-medium">
                            {language === 'en' ? 'Your Feedback' : '您的反馈'}
                          </h4>
                          <div className="grid grid-cols-3 gap-4 text-xs">
                            <div>
                              <p className="text-muted-foreground">
                                {language === 'en' ? 'Accuracy' : '准确度'}
                              </p>
                              {renderStars(reading.feedback.accuracy_rating)}
                            </div>
                            <div>
                              <p className="text-muted-foreground">
                                {language === 'en' ? 'Satisfaction' : '满意度'}
                              </p>
                              {renderStars(reading.feedback.satisfaction_rating)}
                            </div>
                            <div>
                              <p className="text-muted-foreground">
                                {language === 'en' ? 'Usefulness' : '有用度'}
                              </p>
                              {renderStars(reading.feedback.usefulness_rating)}
                            </div>
                          </div>
                          {reading.feedback.feedback_text && (
                            <p className="text-sm mt-2">{reading.feedback.feedback_text}</p>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Feedback Dialog */}
      {selectedReading && (
        <FeedbackDialog
          open={feedbackDialogOpen}
          onOpenChange={setFeedbackDialogOpen}
          readingId={selectedReading.id}
        />
      )}
    </div>
  );
};

export default Profile;
