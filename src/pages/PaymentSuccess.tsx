
import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { CheckCircle, Crown, Calendar, Gift } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';

const PaymentSuccess = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { language } = useLanguage();
  const { subscription, checkSubscription, upgradeToMonthly } = useSubscription();
  const { user, session } = useAuth();
  const [loading, setLoading] = useState(true);

  // 获取Creem返回的参数
  const hasCreemParams = searchParams.get('checkout_id') || searchParams.get('order_id');
  const isSuccessPage = window.location.pathname.includes('success');

  useEffect(() => {
    const handlePaymentSuccess = async () => {
      console.log('🎉 Payment Success Page - Full URL:', window.location.href);
      console.log('🎉 Payment Success Page - URL params:', Object.fromEntries(searchParams.entries()));
      console.log('🎉 Has Creem params:', hasCreemParams);
      console.log('🎉 Is success page:', isSuccessPage);

      if (!hasCreemParams && !isSuccessPage) {
        console.log('⚠️ No payment parameters found, redirecting to pricing');
        navigate('/pricing');
        return;
      }

      try {
        if (hasCreemParams) {
          console.log('Processing Creem payment success with parameters...');

          // 🔧 重要：提取subscription_id和customer_id用于正式付费订阅
          // Creem可能使用不同的参数名，让我们尝试多种可能性
          const subscriptionId = searchParams.get('subscription_id') ||
                                searchParams.get('order_id') ||
                                searchParams.get('checkout_id');
          const customerId = searchParams.get('customer_id');

          console.log('🔧 All URL parameters:', {
            subscription_id: searchParams.get('subscription_id'),
            order_id: searchParams.get('order_id'),
            checkout_id: searchParams.get('checkout_id'),
            customer_id: searchParams.get('customer_id'),
            product_id: searchParams.get('product_id'),
            signature: searchParams.get('signature')
          });

          console.log('🔧 Using as subscription ID:', subscriptionId);

          // 立即尝试手动激活订阅（不等待Webhook）
          console.log('Manually activating subscription with Creem parameters...');
          // 🔧 重要：如果有任何Creem参数，就强制设置为付费订阅
          const forceSubscriptionId = subscriptionId || 'creem_paid_' + Date.now();
          console.log('🔧 Force setting subscription ID:', forceSubscriptionId);
          const { error } = await upgradeToMonthly(forceSubscriptionId, customerId || undefined);

          if (!error) {
            console.log('Manual subscription activation successful');
            toast({
              title: language === 'en' ? '🎉 Welcome to Premium!' : '🎉 欢迎成为高级会员！',
              description: language === 'en' ? 'Your subscription has been activated successfully!' : '您的订阅已成功激活！'
            });
            await checkSubscription();
          } else {
            console.error('Subscription activation failed:', error);
            toast({
              title: language === 'en' ? 'Processing Payment...' : '正在处理支付...',
              description: language === 'en'
                ? 'Your payment is being processed. Please wait a moment.'
                : '您的支付正在处理中，请稍等片刻。'
            });
          }
        } else if (isSuccessPage) {
          console.log('Processing payment success...');

          // 立即尝试手动激活订阅（不等待Webhook）
          console.log('Manually activating subscription...');
          const { error } = await upgradeToMonthly();

          if (!error) {
            console.log('Manual subscription activation successful');
            toast({
              title: language === 'en' ? '🎉 Welcome to Premium!' : '🎉 欢迎成为高级会员！',
              description: language === 'en' ? 'Your subscription has been activated successfully!' : '您的订阅已成功激活！'
            });
          }
        }

        // 等待一下然后检查订阅状态
        setTimeout(async () => {
          await checkSubscription();
          setLoading(false);
        }, 3000);

      } catch (error) {
        console.error('❌ Error handling payment success:', error);
        toast({
          title: language === 'en' ? 'Error' : '错误',
          description: language === 'en' ? 'There was an error processing your payment. Please contact support.' : '处理支付时出错，请联系客服。',
          variant: 'destructive'
        });
        setLoading(false);
      }
    };

    handlePaymentSuccess();
  }, [searchParams, checkSubscription, navigate, upgradeToMonthly, hasCreemParams, isSuccessPage, language]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-mystic-dark via-background to-mystic-dark flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-mystic-purple mx-auto"></div>
          <p className="text-mystic-purple">
            {language === 'en' ? 'Processing your payment...' : '正在处理您的付款...'}
          </p>
        </div>
      </div>
    );
  }

  const isSubscriptionActive = subscription?.status === 'active' &&
    subscription?.subscription_type === 'monthly';  // 🔧 修复：使用 'monthly' 而不是 'premium'

  return (
    <div className="min-h-screen bg-gradient-to-br from-mystic-dark via-background to-mystic-dark pt-20">
      <div className="container mx-auto px-4 max-w-2xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="bg-card/90 backdrop-blur-sm border-mystic-purple/30">
            <CardHeader className="text-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 150 }}
                className="mx-auto mb-4"
              >
                <CheckCircle className="h-16 w-16 text-green-500" />
              </motion.div>

              <CardTitle className="text-2xl font-bold text-mystic-purple">
                {language === 'en' ? '🎉 Payment Successful!' : '🎉 付款成功！'}
              </CardTitle>
            </CardHeader>

            <CardContent className="space-y-6">
              <div className="text-center">
                <p className="text-lg text-muted-foreground">
                  {language === 'en'
                    ? 'Thank you for upgrading to Premium!'
                    : '感谢您升级到高级版！'
                  }
                </p>
              </div>

              {/* 订阅详情 */}
              {subscription && (
                <div className="bg-mystic-dark/20 rounded-lg p-6 space-y-4">
                  <h3 className="font-semibold text-mystic-purple flex items-center gap-2">
                    <Crown className="h-5 w-5" />
                    {language === 'en' ? 'Your Premium Benefits' : '您的高级权益'}
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Gift className="h-4 w-4 text-mystic-pink" />
                      <span>
                        {language === 'en'
                          ? `${subscription.daily_questions_limit || 50} daily questions`
                          : `每日 ${subscription.daily_questions_limit || 50} 次提问`
                        }
                      </span>
                    </div>

                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-mystic-pink" />
                      <span>
                        {language === 'en' ? 'Premium readings' : '高级占卜解读'}
                      </span>
                    </div>
                  </div>

                  {subscription.subscription_end_date && (
                    <div className="pt-4 border-t border-mystic-purple/20">
                      <p className="text-sm text-muted-foreground">
                        {language === 'en' ? 'Valid until:' : '有效期至：'}
                        <span className="font-medium text-mystic-purple ml-2">
                          {new Date(subscription.subscription_end_date).toLocaleDateString(
                            language === 'en' ? 'en-US' : 'zh-CN',
                            {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            }
                          )}
                        </span>
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* 重要提醒 */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-semibold text-blue-800 mb-2">
                  {language === 'en' ? '💡 One-Time Payment Model' : '💡 一次性付费模式'}
                </h4>
                <p className="text-blue-700 text-sm leading-relaxed">
                  {language === 'en'
                    ? 'Your subscription is a one-time payment for this month. No automatic renewals will occur. If you wish to continue next month, you can resubscribe.'
                    : '您的订阅是本月的一次性付费，不会自动续订。如果希望下个月继续使用，您可以重新订阅。'
                  }
                </p>
              </div>

              {/* 操作按钮 */}
              <div className="flex gap-4 pt-4">
                <Button
                  onClick={() => navigate('/')}
                  className="flex-1 bg-gradient-to-r from-mystic-purple to-mystic-pink hover:from-mystic-purple/80 hover:to-mystic-pink/80"
                >
                  {language === 'en' ? 'Start Reading Tarot' : '开始塔罗占卜'}
                </Button>

                <Button
                  onClick={() => navigate('/profile')}
                  variant="outline"
                  className="border-mystic-purple/30 hover:bg-mystic-purple/10"
                >
                  {language === 'en' ? 'View Profile' : '查看个人资料'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default PaymentSuccess;
