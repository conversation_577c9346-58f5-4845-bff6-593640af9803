import React from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { XCircle, ArrowLeft, CreditCard, HelpCircle } from 'lucide-react';
import { Link } from 'react-router-dom';

const PaymentCancel = () => {
  const { language } = useLanguage();

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center px-4">
      <div className="max-w-2xl w-full">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
        >
          <Card className="bg-white/10 backdrop-blur-lg border-white/20 text-white">
            <CardContent className="p-8 text-center">
              {/* Cancel Icon */}
              <motion.div
                className="flex justify-center mb-6"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <XCircle className="h-20 w-20 text-orange-400" />
              </motion.div>

              {/* Title */}
              <motion.h1
                className="text-3xl md:text-4xl font-bold mb-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                {language === 'en' ? 'Payment Cancelled' : '支付已取消'}
              </motion.h1>

              {/* Subtitle */}
              <motion.p
                className="text-xl text-purple-200 mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                {language === 'en'
                  ? 'No worries! Your payment was not processed.'
                  : '没关系！您的付款未被处理。'
                }
              </motion.p>

              {/* Information */}
              <motion.div
                className="bg-gradient-to-r from-orange-600/20 to-red-600/20 rounded-lg p-6 mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                <h3 className="text-lg font-semibold mb-4">
                  {language === 'en' ? 'What happened?' : '发生了什么？'}
                </h3>
                <div className="text-sm text-left space-y-2">
                  <p>
                    {language === 'en'
                      ? '• You cancelled the payment process'
                      : '• 您取消了支付流程'
                    }
                  </p>
                  <p>
                    {language === 'en'
                      ? '• No charges were made to your account'
                      : '• 您的账户未被扣费'
                    }
                  </p>
                  <p>
                    {language === 'en'
                      ? '• You can still enjoy our free features'
                      : '• 您仍可以享受我们的免费功能'
                    }
                  </p>
                </div>
              </motion.div>

              {/* Options */}
              <motion.div
                className="space-y-4 mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                <h3 className="text-lg font-semibold mb-4">
                  {language === 'en' ? 'What would you like to do?' : '您想要做什么？'}
                </h3>

                <div className="grid md:grid-cols-2 gap-4">
                  <Link to="/pricing" className="block">
                    <Button className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600">
                      <CreditCard className="h-4 w-4 mr-2" />
                      {language === 'en' ? 'Try Payment Again' : '重新尝试支付'}
                    </Button>
                  </Link>

                  <Link to="/" className="block">
                    <Button variant="outline" className="w-full border-white/30 text-white hover:bg-white/10">
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      {language === 'en' ? 'Continue with Free' : '继续使用免费版'}
                    </Button>
                  </Link>
                </div>
              </motion.div>

              {/* Registration Reminder */}
              <motion.div
                className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-lg p-6 mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.7 }}
              >
                <h3 className="text-lg font-semibold mb-2">
                  {language === 'en' ? 'New to Mystic Mirror?' : '初次使用神秘镜像？'}
                </h3>
                <p className="text-sm text-purple-200 mb-4">
                  {language === 'en'
                    ? 'New users automatically get a 3-day premium experience upon registration. Experience all features before deciding to subscribe.'
                    : '新用户注册后自动获得3天高级体验。在决定订阅之前体验所有功能。'
                  }
                </p>
                <Link to="/auth">
                  <Button variant="outline" className="border-blue-300/30 text-white hover:bg-blue-500/20">
                    {language === 'en' ? 'Sign Up Now' : '立即注册'}
                  </Button>
                </Link>
              </motion.div>

              {/* Support */}
              <motion.div
                className="pt-6 border-t border-white/20"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.8 }}
              >
                <div className="flex items-center justify-center mb-4">
                  <HelpCircle className="h-5 w-5 text-purple-300 mr-2" />
                  <span className="text-sm">
                    {language === 'en' ? 'Need help with payment?' : '需要支付帮助？'}
                  </span>
                </div>
                <Link to="/support">
                  <Button variant="ghost" className="text-purple-300 hover:text-white hover:bg-purple-500/20">
                    {language === 'en' ? 'Contact Support' : '联系支持'}
                  </Button>
                </Link>
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Floating Elements */}
        <motion.div
          className="absolute top-10 left-10 text-orange-300"
          animate={{
            y: [0, -10, 0],
            rotate: [0, 5, 0]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <XCircle className="h-6 w-6" />
        </motion.div>

        <motion.div
          className="absolute top-20 right-20 text-purple-300"
          animate={{
            y: [0, 10, 0],
            rotate: [0, -5, 0]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        >
          <CreditCard className="h-8 w-8" />
        </motion.div>
      </div>
    </div>
  );
};

export default PaymentCancel;
