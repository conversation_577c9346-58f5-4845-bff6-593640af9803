import React from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Mail, MessageCircle, Phone, Clock, Globe, Shield } from 'lucide-react';
import { Link } from 'react-router-dom';

const Support = () => {
  const { language } = useLanguage();

  const contactMethods = [
    {
      icon: Mail,
      title: language === 'en' ? 'Email Support' : '邮件支持',
      description: language === 'en' ? 'Get help via email within 24 hours' : '24小时内通过邮件获得帮助',
      contact: '<EMAIL>',
      action: language === 'en' ? 'Send Email' : '发送邮件'
    },
    {
      icon: MessageCircle,
      title: language === 'en' ? 'WeChat Support' : '微信支持',
      description: language === 'en' ? 'Chat with us on WeChat for instant help' : '通过微信与我们聊天获得即时帮助',
      contact: 'zhidasuc',
      action: language === 'en' ? 'Add WeChat' : '添加微信'
    },
    {
      icon: Globe,
      title: language === 'en' ? 'Instagram' : 'Instagram',
      description: language === 'en' ? 'Follow us for updates and tips' : '关注我们获取更新和技巧',
      contact: '@mystic_mirror_vip',
      action: language === 'en' ? 'Follow Us' : '关注我们'
    }
  ];

  const faqItems = [
    {
      question: language === 'en' ? 'How accurate are the tarot readings?' : '塔罗占卜的准确性如何？',
      answer: language === 'en'
        ? 'Our AI-powered tarot readings are designed to provide insightful guidance based on traditional tarot interpretations. While we cannot guarantee specific outcomes, many users find our readings helpful for reflection and decision-making.'
        : '我们的AI塔罗占卜基于传统塔罗解释设计，旨在提供有洞察力的指导。虽然我们不能保证特定结果，但许多用户发现我们的占卜对反思和决策很有帮助。'
    },
    {
      question: language === 'en' ? 'Can I try before subscribing?' : '可以在订阅前试用吗？',
      answer: language === 'en'
        ? 'Yes! We offer a 3-day free trial that gives you full access to all premium features. This allows you to experience our service and decide if it\'s right for you before committing to a subscription.'
        : '当然可以！我们提供3天免费试用，让您完全体验所有高级功能。这让您可以在决定订阅之前充分了解我们的服务是否适合您。'
    },

    {
      question: language === 'en' ? 'Is my personal information secure?' : '我的个人信息安全吗？',
      answer: language === 'en'
        ? 'Yes, we take your privacy seriously. All personal information is encrypted and stored securely. We never share your data with third parties without your consent.'
        : '是的，我们非常重视您的隐私。所有个人信息都经过加密并安全存储。未经您同意，我们绝不会与第三方分享您的数据。'
    },
    {
      question: language === 'en' ? 'Can I use the service on mobile devices?' : '可以在移动设备上使用服务吗？',
      answer: language === 'en'
        ? 'Absolutely! Our website is fully responsive and works perfectly on all mobile devices and tablets.'
        : '当然可以！我们的网站完全响应式，在所有移动设备和平板电脑上都能完美运行。'
    },
    {
      question: language === 'en' ? 'What languages are supported?' : '支持哪些语言？',
      answer: language === 'en'
        ? 'Currently, we support English and Chinese. We are working on adding more languages in the future.'
        : '目前我们支持英语和中文。我们正在努力在未来添加更多语言。'
    }
  ];

  const businessHours = [
    {
      day: language === 'en' ? 'Monday - Friday' : '周一 - 周五',
      hours: '9:00 AM - 6:00 PM (PST)'
    },
    {
      day: language === 'en' ? 'Saturday' : '周六',
      hours: '10:00 AM - 4:00 PM (PST)'
    },
    {
      day: language === 'en' ? 'Sunday' : '周日',
      hours: language === 'en' ? 'Closed' : '休息'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 py-12 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <motion.h1
            className="text-4xl md:text-5xl font-bold text-white mb-4"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {language === 'en' ? 'Support Center' : '支持中心'}
          </motion.h1>
          <motion.p
            className="text-xl text-purple-200 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {language === 'en'
              ? 'We are here to help you with any questions or concerns'
              : '我们在这里帮助您解决任何问题或疑虑'
            }
          </motion.p>
        </div>

        {/* Contact Methods */}
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {contactMethods.map((method, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * index }}
            >
              <Card className="bg-white/10 backdrop-blur-lg border-white/20 text-white h-full hover:bg-white/15 transition-colors">
                <CardHeader className="text-center">
                  <div className="flex justify-center mb-4">
                    <method.icon className="h-12 w-12 text-purple-300" />
                  </div>
                  <CardTitle className="text-xl mb-2">{method.title}</CardTitle>
                  <p className="text-purple-200 text-sm">{method.description}</p>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-lg font-semibold mb-4">{method.contact}</p>
                  <Button
                    variant="outline"
                    className="border-purple-300/30 text-white hover:bg-purple-500/20"
                    onClick={() => {
                      if (method.contact.includes('@')) {
                        window.open(`mailto:${method.contact}`, '_blank');
                      } else if (method.contact.includes('zhidasuc')) {
                        // WeChat or Instagram
                        window.open(`https://instagram.com/${method.contact}`, '_blank');
                      }
                    }}
                  >
                    {method.action}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Business Hours */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Card className="bg-white/10 backdrop-blur-lg border-white/20 text-white">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <Clock className="h-12 w-12 text-purple-300" />
              </div>
              <CardTitle className="text-2xl">
                {language === 'en' ? 'Business Hours' : '营业时间'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {businessHours.map((schedule, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-white/10 last:border-b-0">
                    <span className="font-medium">{schedule.day}</span>
                    <span className="text-purple-200">{schedule.hours}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* FAQ Section */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <h2 className="text-3xl font-bold text-white text-center mb-8">
            {language === 'en' ? 'Frequently Asked Questions' : '常见问题'}
          </h2>
          <div className="space-y-6">
            {faqItems.map((item, index) => (
              <Card key={index} className="bg-white/10 backdrop-blur-lg border-white/20 text-white">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold mb-3 text-purple-200">{item.question}</h3>
                  <p className="text-white/90 leading-relaxed">{item.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </motion.div>

        {/* Security Notice */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <Card className="bg-gradient-to-r from-green-600/20 to-blue-600/20 backdrop-blur-lg border-green-300/30 text-white">
            <CardContent className="p-6 text-center">
              <div className="flex justify-center mb-4">
                <Shield className="h-12 w-12 text-green-400" />
              </div>
              <h3 className="text-xl font-semibold mb-2">
                {language === 'en' ? 'Your Privacy is Protected' : '您的隐私受到保护'}
              </h3>
              <p className="text-green-100">
                {language === 'en'
                  ? 'All communications are encrypted and your personal information is never shared with third parties.'
                  : '所有通信都经过加密，您的个人信息绝不会与第三方分享。'
                }
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Back to Home */}
        <div className="text-center">
          <Link to="/">
            <Button variant="outline" className="border-white/30 text-white hover:bg-white/10">
              {language === 'en' ? 'Back to Home' : '返回首页'}
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Support;
