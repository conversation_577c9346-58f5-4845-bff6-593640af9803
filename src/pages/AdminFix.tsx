import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react';

const AdminFix: React.FC = () => {
  const { user } = useAuth();
  const { fixNewUserTrial, subscription, refreshSubscription } = useSubscription();
  const [isFixing, setIsFixing] = useState(false);
  const [fixResult, setFixResult] = useState<{ success?: boolean; error?: any } | null>(null);

  const handleFixTrial = async () => {
    setIsFixing(true);
    setFixResult(null);
    
    try {
      const result = await fixNewUserTrial();
      setFixResult(result);
    } catch (error) {
      setFixResult({ error });
    } finally {
      setIsFixing(false);
    }
  };

  const handleRefreshStatus = async () => {
    await refreshSubscription();
  };

  if (!user) {
    return (
      <div className="container mx-auto p-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            请先登录以使用管理功能
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>🔧 用户试用期修复工具</CardTitle>
          <CardDescription>
            用于修复新注册用户没有获得试用期的问题
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 当前用户信息 */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">当前用户信息</h3>
            <div className="space-y-1 text-sm">
              <p><strong>邮箱:</strong> {user.email}</p>
              <p><strong>用户ID:</strong> {user.id}</p>
            </div>
          </div>

          {/* 当前订阅状态 */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">当前订阅状态</h3>
            {subscription ? (
              <div className="space-y-1 text-sm">
                <p><strong>订阅类型:</strong> {subscription.subscription_type || '无'}</p>
                <p><strong>状态:</strong> {subscription.status || '无'}</p>
                <p><strong>每日限制:</strong> {subscription.daily_questions_limit || '无'}</p>
                <p><strong>试用开始:</strong> {subscription.trial_start_date ? new Date(subscription.trial_start_date).toLocaleString() : '无'}</p>
                <p><strong>试用结束:</strong> {subscription.trial_end_date ? new Date(subscription.trial_end_date).toLocaleString() : '无'}</p>
                <p><strong>已使用试用:</strong> {subscription.trial_used ? '是' : '否'}</p>
              </div>
            ) : (
              <p className="text-sm text-gray-500">没有订阅记录</p>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="space-y-3">
            <Button 
              onClick={handleFixTrial} 
              disabled={isFixing}
              className="w-full"
            >
              {isFixing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  修复中...
                </>
              ) : (
                '🔧 修复试用期'
              )}
            </Button>

            <Button 
              onClick={handleRefreshStatus} 
              variant="outline"
              className="w-full"
            >
              🔄 刷新状态
            </Button>
          </div>

          {/* 修复结果 */}
          {fixResult && (
            <Alert className={fixResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
              {fixResult.success ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription>
                {fixResult.success ? (
                  <div>
                    <p className="font-semibold text-green-800">✅ 修复成功！</p>
                    <p className="text-green-700">用户已获得3天试用期，每天20次提问。</p>
                  </div>
                ) : (
                  <div>
                    <p className="font-semibold text-red-800">❌ 修复失败</p>
                    <p className="text-red-700">错误: {fixResult.error?.message || '未知错误'}</p>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}

          {/* 说明信息 */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2 text-blue-800">📋 修复说明</h3>
            <div className="space-y-1 text-sm text-blue-700">
              <p>• 此工具会为当前用户创建或更新试用期订阅</p>
              <p>• 试用期：3天，每天20次提问</p>
              <p>• 适用于新注册但没有获得试用期的用户</p>
              <p>• 如果用户已有订阅，会被覆盖为试用期</p>
            </div>
          </div>

          {/* 访问说明 */}
          <div className="bg-yellow-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2 text-yellow-800">⚠️ 访问方式</h3>
            <div className="space-y-1 text-sm text-yellow-700">
              <p>• 访问路径: /admin-fix</p>
              <p>• 仅用于临时修复，修复完成后应删除此页面</p>
              <p>• 建议修复数据库触发器以自动处理新用户</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminFix;
