
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Eye, EyeOff } from 'lucide-react';

const Auth = () => {
  const { signIn, signUp } = useAuth();
  const { language } = useLanguage();
  const navigate = useNavigate();
  
  const [isLogin, setIsLogin] = useState(true);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    username: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (isLogin) {
        const { error } = await signIn(formData.email, formData.password);
        if (error) {
          toast({
            title: language === 'en' ? 'Error' : '错误',
            description: language === 'en' ? error.message : '登录失败，请检查邮箱和密码',
            variant: 'destructive'
          });
        } else {
          toast({
            title: language === 'en' ? 'Welcome back!' : '欢迎回来！',
            description: language === 'en' ? 'Successfully signed in' : '登录成功'
          });
          navigate('/');
        }
      } else {
        const { error } = await signUp(formData.email, formData.password, formData.username);
        if (error) {
          toast({
            title: language === 'en' ? 'Error' : '错误',
            description: language === 'en' ? error.message : '注册失败，请重试',
            variant: 'destructive'
          });
        } else {
          toast({
            title: language === 'en' ? 'Account created!' : '账户创建成功！',
            description: language === 'en' ? 'You can now start using the app' : '您现在可以开始使用应用了'
          });
          navigate('/');
        }
      }
    } catch (error) {
      toast({
        title: language === 'en' ? 'Error' : '错误',
        description: language === 'en' ? 'An unexpected error occurred' : '发生意外错误',
        variant: 'destructive'
      });
    }

    setLoading(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="bg-card border-mystic-purple/20">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl text-mystic-purple">
              {isLogin 
                ? (language === 'en' ? 'Welcome Back' : '欢迎回来')
                : (language === 'en' ? 'Create Account' : '创建账户')
              }
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {!isLogin && (
                <div>
                  <Input
                    name="username"
                    type="text"
                    placeholder={language === 'en' ? 'Username' : '用户名'}
                    value={formData.username}
                    onChange={handleInputChange}
                    required={!isLogin}
                    className="bg-background border-mystic-purple/30"
                  />
                </div>
              )}
              
              <div>
                <Input
                  name="email"
                  type="email"
                  placeholder={language === 'en' ? 'Email' : '邮箱'}
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="bg-background border-mystic-purple/30"
                />
              </div>
              
              <div className="relative">
                <Input
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder={language === 'en' ? 'Password' : '密码'}
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  className="bg-background border-mystic-purple/30 pr-10"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>

              <Button
                type="submit"
                disabled={loading}
                className="w-full bg-mystic-purple hover:bg-mystic-purple/90"
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isLogin 
                  ? (language === 'en' ? 'Sign In' : '登录')
                  : (language === 'en' ? 'Sign Up' : '注册')
                }
              </Button>
            </form>

            <div className="mt-4 text-center">
              <button
                type="button"
                onClick={() => setIsLogin(!isLogin)}
                className="text-mystic-purple hover:underline text-sm"
              >
                {isLogin 
                  ? (language === 'en' ? 'Need an account? Sign up' : '需要账户？注册')
                  : (language === 'en' ? 'Already have an account? Sign in' : '已有账户？登录')
                }
              </button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default Auth;
