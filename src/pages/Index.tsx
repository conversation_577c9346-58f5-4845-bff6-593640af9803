
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import Header from '@/components/Header';
import SpreadSelector from '@/components/SpreadSelector';
import ReadingResult from '@/components/ReadingResult';
import DitherBackground from '@/components/DitherBackground';
import { generateRandomCards } from '@/data/tarotCards';
import { Button } from '@/components/ui/button';

const Index = () => {
  const { t, language } = useLanguage();
  const { user } = useAuth();
  const {
    canAskQuestion,
    questionsRemaining,
    isTrialActive,
    isSubscriptionActive,
    daysRemainingInTrial
  } = useSubscription();
  const [step, setStep] = useState<'welcome' | 'login-prompt' | 'select-spread' | 'reading'>('welcome');
  const [spreadType, setSpreadType] = useState<string>('');
  const [userQuestion, setUserQuestion] = useState<string>('');
  const [selectedCards, setSelectedCards] = useState<any[]>([]);

  const handleSelectSpread = (type: string, count: number, question: string) => {
    setSpreadType(type);
    setUserQuestion(question);

    // Automatically draw cards for the user
    const drawnCards = generateRandomCards(count);
    setSelectedCards(drawnCards);

    // Move to reading step
    setStep('reading');
  };

  const startNewReading = () => {
    setStep('select-spread');
    setSelectedCards([]);
    setUserQuestion('');
  };

  // 🔧 新增：处理Begin Your Reading按钮点击
  const handleBeginReading = () => {
    if (!user) {
      // 未登录用户显示登录提示
      setStep('login-prompt');
    } else {
      // 已登录用户直接开始占卜
      setStep('select-spread');
    }
  };

  // The Sun card image URL
  const sunCardUrl = "https://upload.wikimedia.org/wikipedia/commons/1/17/RWS_Tarot_19_Sun.jpg";

  return (
    <div className="min-h-screen bg-background text-foreground relative overflow-hidden">
      {/* Dither background effect */}
      <div className="absolute inset-0 z-0">
        <DitherBackground
          waveSpeed={0.02}
          waveFrequency={2}
          waveAmplitude={0.2}
          waveColor={[0.3, 0.2, 0.5]}
          colorNum={6}
          pixelSize={3}
          disableAnimation={false}
          enableMouseInteraction={true}
          mouseRadius={0.8}
        />
      </div>
      
      <div className="relative z-10">
        <Header onNewReading={step !== 'welcome' ? startNewReading : undefined} />

        <main className="container mx-auto px-4 py-8 mb-16 pt-24">
          {step === 'welcome' && (
            <div className="flex flex-col items-center justify-center min-h-[80vh]">
              <motion.div
                className="text-center max-w-2xl"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="text-6xl mb-6 text-mystic-gold animate-glow">✧</div>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-mystic-purple">
                  {t('welcome.title')}
                </h1>
                <p className="text-xl md:text-2xl text-muted-foreground mb-10">
                  {t('welcome.subtitle')}
                </p>

                {/* 登录提示 - 仅对未登录用户显示 */}
                {!user && (
                  <div className="mb-8 p-4 bg-mystic-purple/10 rounded-lg border border-mystic-purple/20 backdrop-blur-sm">
                    <p className="text-mystic-purple mb-2 font-semibold">
                      {language === 'en'
                        ? '🎁 New users get 3-day premium membership!'
                        : '🎁 新用户获得3天高级会员！'
                      }
                    </p>
                    <p className="text-mystic-purple/80 mb-4 text-sm">
                      {language === 'en'
                        ? 'Sign in to save your reading history and get premium features!'
                        : '登录以保存您的占卜历史并获得高级功能！'
                      }
                    </p>
                    <div className="flex justify-center space-x-2">
                      <Link to="/auth">
                        <Button variant="outline" className="border-mystic-purple/30 backdrop-blur-sm">
                          {language === 'en' ? 'Sign In / Sign Up' : '登录 / 注册'}
                        </Button>
                      </Link>
                      <Link to="/pricing">
                        <Button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600">
                          {language === 'en' ? 'View Plans' : '查看套餐'}
                        </Button>
                      </Link>
                    </div>
                  </div>
                )}

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <button
                    onClick={handleBeginReading}
                    className="bg-mystic-purple hover:bg-mystic-purple/90 text-white font-bold py-4 px-8 rounded-lg text-lg shadow-lg backdrop-blur-sm"
                  >
                    {t('welcome.start')}
                  </button>
                </motion.div>

                <div className="mt-16 flex justify-center space-x-8">
                  {/* Fixed three Sun cards */}
                  <motion.div
                    className="w-32 h-48 rounded-lg bg-card-gradient shadow-lg transform rotate-[-15deg] border border-mystic-purple/30 overflow-hidden backdrop-blur-sm"
                    animate={{ y: [0, -10, 0] }}
                    transition={{
                      repeat: Infinity,
                      duration: 6,
                      ease: "easeInOut",
                    }}
                  >
                    <img src={sunCardUrl} alt="The Sun" className="w-full h-full object-cover" />
                  </motion.div>
                  <motion.div
                    className="w-32 h-48 rounded-lg bg-card-gradient shadow-lg border border-mystic-purple/30 overflow-hidden backdrop-blur-sm"
                    animate={{ y: [0, -15, 0] }}
                    transition={{
                      repeat: Infinity,
                      duration: 5,
                      ease: "easeInOut",
                      delay: 0.3
                    }}
                  >
                    <img src={sunCardUrl} alt="The Sun" className="w-full h-full object-cover" />
                  </motion.div>
                  <motion.div
                    className="w-32 h-48 rounded-lg bg-card-gradient shadow-lg transform rotate-[15deg] border border-mystic-purple/30 overflow-hidden backdrop-blur-sm"
                    animate={{ y: [0, -10, 0] }}
                    transition={{
                      repeat: Infinity,
                      duration: 5.5,
                      ease: "easeInOut",
                      delay: 0.6
                    }}
                  >
                    <img src={sunCardUrl} alt="The Sun" className="w-full h-full object-cover" />
                  </motion.div>
                </div>
              </motion.div>
            </div>
          )}

          {step === 'login-prompt' && (
            <div className="flex flex-col items-center justify-center min-h-[80vh]">
              <motion.div
                className="text-center max-w-2xl"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="text-6xl mb-6 text-mystic-gold">🔮</div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6 text-mystic-purple">
                  {language === 'en' ? 'Sign in to Continue' : '登录以继续'}
                </h2>

                <div className="mb-8 p-6 bg-gradient-to-r from-mystic-purple/10 to-mystic-pink/10 rounded-lg border border-mystic-purple/20 backdrop-blur-sm">
                  <p className="text-lg text-mystic-purple mb-4">
                    {language === 'en'
                      ? 'Sign in to save your reading history and get premium features!'
                      : '登录以保存您的占卜历史并获得高级功能！'
                    }
                  </p>

                  <div className="space-y-3 text-sm text-muted-foreground">
                    <div className="flex items-center justify-center space-x-2">
                      <span>🎁</span>
                      <span>
                        {language === 'en'
                          ? 'New users get 3-day premium membership (20 readings/day)'
                          : '新用户获得3天高级会员（每天20次占卜）'
                        }
                      </span>
                    </div>
                    <div className="flex items-center justify-center space-x-2">
                      <span>✨</span>
                      <span>
                        {language === 'en'
                          ? 'Then enjoy 1 free AI tarot reading daily'
                          : '之后每日享受1次免费AI塔罗解读'
                        }
                      </span>
                    </div>
                    <div className="flex items-center justify-center space-x-2">
                      <span>📚</span>
                      <span>
                        {language === 'en'
                          ? 'Save and review your reading history'
                          : '保存并回顾您的占卜历史'
                        }
                      </span>
                    </div>
                    <div className="flex items-center justify-center space-x-2">
                      <span>⭐</span>
                      <span>
                        {language === 'en'
                          ? 'Rate and provide feedback on readings'
                          : '对占卜进行评分和反馈'
                        }
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex justify-center space-x-4 mb-6">
                  <Link to="/auth">
                    <Button className="bg-gradient-to-r from-mystic-purple to-mystic-pink hover:from-mystic-purple/80 hover:to-mystic-pink/80 text-white px-8 py-3">
                      {language === 'en' ? 'Sign In / Sign Up' : '登录 / 注册'}
                    </Button>
                  </Link>
                  <Button
                    variant="outline"
                    className="border-mystic-purple/30 text-mystic-purple px-8 py-3 backdrop-blur-sm"
                    onClick={() => setStep('welcome')}
                  >
                    {language === 'en' ? 'Back' : '返回'}
                  </Button>
                </div>

                <p className="text-sm text-muted-foreground">
                  {language === 'en'
                    ? 'Already have premium? '
                    : '已有高级会员？'
                  }
                  <Link to="/pricing" className="text-mystic-purple hover:underline">
                    {language === 'en' ? 'View Plans' : '查看套餐'}
                  </Link>
                </p>
              </motion.div>
            </div>
          )}

          {step === 'select-spread' && (
            <SpreadSelector onSelectSpread={handleSelectSpread} />
          )}

          {step === 'reading' && (
            <ReadingResult
              selectedCards={selectedCards}
              spreadType={spreadType}
              userQuestion={userQuestion}
              onNewReading={startNewReading}
            />
          )}
        </main>

        <footer className="w-full py-6 bg-mystic-dark/80 border-t border-mystic-purple/20 backdrop-blur-sm">
          <div className="container mx-auto px-4 text-center text-muted-foreground text-sm">
            <p>Mystic Card Mirror - Tarot Reading</p>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default Index;
