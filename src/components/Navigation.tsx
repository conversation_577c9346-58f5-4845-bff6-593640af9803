
import React from 'react';
import { Link } from 'react-router-dom';
import { Globe, User, LogIn, Crown, CreditCard, HelpCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/contexts/SubscriptionContext';

const Navigation = () => {
  const { language, setLanguage, t } = useLanguage();
  const { user } = useAuth();
  const { isSubscriptionActive, isTrialActive, questionsRemaining, currentDailyLimit } = useSubscription();

  // 计算已用次数/总次数的显示格式
  const getUsageDisplay = () => {
    const limit = currentDailyLimit || 1; // 使用实际的当前限制
    const used = limit - questionsRemaining; // 从剩余次数反推已用次数

    return `${used}/${limit}`;
  };

  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'zh' : 'en');
  };

  return (
    <nav className="w-full py-4 bg-mystic-dark/50 backdrop-blur-md fixed top-0 z-50 border-b border-mystic-purple/20">
      <div className="container mx-auto px-4 flex items-center justify-between">
        <div className="flex items-center space-x-6">
          <Link to="/" className="text-xl font-bold text-mystic-purple">
            {language === 'en' ? 'Mystic Mirror' : '神秘镜像'}
          </Link>
          <div className="hidden md:flex space-x-6">
            <Link to="/" className="text-mystic-light hover:text-mystic-purple transition-colors">
              {t('nav.home')}
            </Link>

            <Link to="/pricing" className="text-mystic-light hover:text-mystic-purple transition-colors flex items-center">
              <CreditCard className="h-4 w-4 mr-1" />
              {language === 'en' ? 'Pricing' : '定价'}
            </Link>
            <Link to="/support" className="text-mystic-light hover:text-mystic-purple transition-colors flex items-center">
              <HelpCircle className="h-4 w-4 mr-1" />
              {language === 'en' ? 'Support' : '支持'}
            </Link>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* 订阅状态显示 */}
          {user && (
            <div className="hidden md:flex items-center space-x-2">
              {isSubscriptionActive && (
                <div className="flex items-center space-x-2">
                  <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white">
                    <Crown className="h-3 w-3 mr-1" />
                    {language === 'en' ? 'Premium' : '高级会员'}
                  </Badge>
                  <Badge variant="outline" className="text-yellow-600 border-yellow-500/30 bg-yellow-50/10">
                    {getUsageDisplay()}
                  </Badge>
                </div>
              )}
              {isTrialActive && (
                <div className="flex items-center space-x-2">
                  <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                    {language === 'en' ? 'Trial' : '试用中'}
                  </Badge>
                  <Badge variant="outline" className="text-purple-600 border-purple-500/30 bg-purple-50/10">
                    {getUsageDisplay()}
                  </Badge>
                </div>
              )}
              {!isSubscriptionActive && !isTrialActive && (
                <Badge variant="outline" className="text-mystic-purple border-mystic-purple">
                  {getUsageDisplay()}
                </Badge>
              )}
            </div>
          )}

          <Button
            variant="ghost"
            size="icon"
            onClick={toggleLanguage}
            className="text-mystic-purple hover:bg-mystic-purple/20"
          >
            <Globe className="h-5 w-5" />
            <span className="ml-2">{language.toUpperCase()}</span>
          </Button>

          {user ? (
            <Link to="/profile">
              <Button
                variant="ghost"
                size="icon"
                className="text-mystic-purple hover:bg-mystic-purple/20"
              >
                <User className="h-5 w-5" />
              </Button>
            </Link>
          ) : (
            <Link to="/auth">
              <Button
                variant="ghost"
                size="icon"
                className="text-mystic-purple hover:bg-mystic-purple/20"
              >
                <LogIn className="h-5 w-5" />
              </Button>
            </Link>
          )}
        </div>
      </div>

      {/* 移动端会员状态显示 */}
      {user && (
        <div className="md:hidden px-4 py-2 bg-mystic-dark/30 border-b border-mystic-purple/10">
          <div className="flex items-center justify-center space-x-2">
            {isSubscriptionActive && (
              <>
                <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-xs">
                  <Crown className="h-3 w-3 mr-1" />
                  {language === 'en' ? 'Premium' : '高级会员'}
                </Badge>
                <Badge variant="outline" className="text-yellow-600 border-yellow-500/30 bg-yellow-50/10 text-xs">
                  {getUsageDisplay()}
                </Badge>
              </>
            )}
            {isTrialActive && (
              <>
                <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs">
                  {language === 'en' ? 'Trial' : '试用中'}
                </Badge>
                <Badge variant="outline" className="text-purple-600 border-purple-500/30 bg-purple-50/10 text-xs">
                  {getUsageDisplay()}
                </Badge>
              </>
            )}
            {!isSubscriptionActive && !isTrialActive && (
              <Badge variant="outline" className="text-mystic-purple border-mystic-purple text-xs">
                {getUsageDisplay()}
              </Badge>
            )}
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navigation;
