import React from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '@/contexts/LanguageContext';

const Footer = () => {
  const { language } = useLanguage();

  return (
    <footer className="bg-black/20 backdrop-blur-lg border-t border-white/10 mt-auto">
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="text-white font-semibold mb-4">
              {language === 'en' ? 'Mystic Mirror' : '神秘镜像'}
            </h3>
            <p className="text-purple-200 text-sm">
              {language === 'en'
                ? 'AI-powered tarot readings for guidance and insight.'
                : 'AI驱动的塔罗占卜，为您提供指导和洞察。'
              }
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-white font-semibold mb-4">
              {language === 'en' ? 'Quick Links' : '快速链接'}
            </h4>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-purple-200 hover:text-white text-sm transition-colors">
                  {language === 'en' ? 'Home' : '首页'}
                </Link>
              </li>
              <li>
                <Link to="/pricing" className="text-purple-200 hover:text-white text-sm transition-colors">
                  {language === 'en' ? 'Pricing' : '定价'}
                </Link>
              </li>
              <li>
                <Link to="/support" className="text-purple-200 hover:text-white text-sm transition-colors">
                  {language === 'en' ? 'Support' : '支持'}
                </Link>
              </li>

            </ul>
          </div>

          {/* Legal */}
          <div>
            <h4 className="text-white font-semibold mb-4">
              {language === 'en' ? 'Legal' : '法律'}
            </h4>
            <ul className="space-y-2">
              <li>
                <Link to="/privacy" className="text-purple-200 hover:text-white text-sm transition-colors">
                  {language === 'en' ? 'Privacy Policy' : '隐私政策'}
                </Link>
              </li>
              <li>
                <Link to="/terms" className="text-purple-200 hover:text-white text-sm transition-colors">
                  {language === 'en' ? 'Terms of Service' : '服务条款'}
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h4 className="text-white font-semibold mb-4">
              {language === 'en' ? 'Contact' : '联系我们'}
            </h4>
            <ul className="space-y-2">
              <li>
                <a
                  href="mailto:<EMAIL>"
                  className="text-purple-200 hover:text-white text-sm transition-colors"
                >
                  <EMAIL>
                </a>
              </li>
              <li>
                <span className="text-purple-200 text-sm">
                  {language === 'en' ? 'WeChat: zhidasuc' : '微信: zhidasuc'}
                </span>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-white/10 mt-8 pt-6 text-center">
          <p className="text-purple-200 text-sm">
            © 2025 Mystic Mirror. {language === 'en' ? 'All rights reserved.' : '版权所有。'}
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
