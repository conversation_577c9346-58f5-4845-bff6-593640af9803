import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, Languages } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { translateTarotReading } from '@/utils/api/tarotReadings';

interface ReadingLanguageToggleProps {
  currentReading: string;
  onTranslationComplete: (translatedReading: string, newLanguage: 'en' | 'zh') => void;
  originalQuestion?: string;
  cards?: Array<{name: string, position: "upright" | "reversed", meaning: string}>;
  disabled?: boolean;
}

const ReadingLanguageToggle: React.FC<ReadingLanguageToggleProps> = ({
  currentReading,
  onTranslationComplete,
  originalQuestion,
  cards,
  disabled = false
}) => {
  const { language } = useLanguage();
  const [isTranslating, setIsTranslating] = useState(false);

  const handleTranslate = async () => {
    if (!currentReading || isTranslating || disabled) return;

    setIsTranslating(true);
    
    try {
      // 确定目标语言（切换到相反的语言）
      const targetLanguage = language === 'en' ? 'zh' : 'en';
      
      console.log(`🌐 Translating from ${language} to ${targetLanguage}`);
      
      const translatedReading = await translateTarotReading(
        currentReading,
        targetLanguage,
        originalQuestion,
        cards
      );
      
      if (translatedReading) {
        onTranslationComplete(translatedReading, targetLanguage);
      }
    } catch (error) {
      console.error('Translation failed:', error);
    } finally {
      setIsTranslating(false);
    }
  };

  const getButtonText = () => {
    if (isTranslating) {
      return language === 'zh' ? '翻译中...' : 'Translating...';
    }

    return language === 'zh'
      ? '🌐 翻译为英文'
      : '🌐 Translate to Chinese';
  };

  const getTooltipText = () => {
    return language === 'zh'
      ? '将当前解读翻译成英文'
      : 'Translate current reading to Chinese';
  };

  if (!currentReading) return null;

  return (
    <div className="flex justify-center mt-4">
      <Button
        variant="outline"
        size="sm"
        onClick={handleTranslate}
        disabled={isTranslating || disabled}
        className="border-mystic-purple text-mystic-purple hover:bg-mystic-purple hover:text-white transition-colors"
        title={getTooltipText()}
      >
        {isTranslating ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <Languages className="mr-2 h-4 w-4" />
        )}
        {getButtonText()}
      </Button>
    </div>
  );
};

export default ReadingLanguageToggle;
