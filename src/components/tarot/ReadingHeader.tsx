
import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

interface ReadingHeaderProps {
  spreadType: string;
  userQuestion: string;
}

const ReadingHeader: React.FC<ReadingHeaderProps> = ({
  spreadType,
  userQuestion
}) => {
  const { t, language } = useLanguage();
  
  // Get appropriate spread description based on current language
  const getLocalizedSpreadType = () => {
    // This function translates the spread type for display
    switch (spreadType) {
      case "Single Card":
        return language === 'zh' ? "单张牌阵" : "Single Card";
      case "Three Card":
        return language === 'zh' ? "三张牌阵" : "Three Card";
      case "Celtic Cross":
        return language === 'zh' ? "凯尔特十字牌阵" : "Celtic Cross";
      default:
        return spreadType;
    }
  };

  return (
    <>
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-mystic-purple mb-2">
          {language === 'en' 
            ? `Your ${spreadType} Reading` 
            : `您的${getLocalizedSpreadType()}解读`}
        </h2>
      </div>

      <div className="bg-card rounded-lg p-6 mb-8 shadow-lg">
        <h3 className="text-xl font-medium text-mystic-purple mb-2">{t('reading.question')}</h3>
        <p className="text-lg">{userQuestion || (language === 'en' ? "What should I focus on right now?" : "我现在应该关注什么?")}</p>
      </div>
    </>
  );
};

export default ReadingHeader;
