
import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/button';
import { Star } from 'lucide-react';
import { motion } from 'framer-motion';
import InterpretationDisplay from './InterpretationDisplay';
import FollowUpQuestions from './FollowUpQuestions';
import NewReadingButton from './NewReadingButton';

interface ReadingDisplayProps {
  loading: boolean;
  displayedText: string;
  isComplete: boolean;
  onNewReading: () => void;
  cards: Array<{name: string, position: "upright" | "reversed", meaning: string}>;
  spreadType: string;
  userQuestion: string;
  // 🌐 翻译相关props
  showTranslateButton?: boolean;
  currentReading?: string;
  onTranslationComplete?: (translatedReading: string, newLanguage: 'en' | 'zh') => void;
  originalQuestion?: string;
  translationDisabled?: boolean;
  // 📊 评分相关props
  showRateButton?: boolean;
  readingId?: string | null;
  onRateClick?: () => void;
}

const ReadingDisplay: React.FC<ReadingDisplayProps> = ({
  loading,
  displayedText,
  isComplete,
  onNewReading,
  cards,
  spreadType,
  userQuestion,
  showTranslateButton,
  currentReading,
  onTranslationComplete,
  originalQuestion,
  translationDisabled,
  showRateButton,
  readingId,
  onRateClick
}) => {
  const { language } = useLanguage();

  return (
    <>
      <InterpretationDisplay
        loading={loading}
        displayedText={displayedText}
        showTranslateButton={showTranslateButton}
        currentReading={currentReading}
        onTranslationComplete={onTranslationComplete}
        originalQuestion={originalQuestion}
        translationDisabled={translationDisabled}
        cards={cards}
      />

      {/* Follow-up Questions Section (Only show when initial reading is complete) */}
      {!loading && isComplete && (
        <FollowUpQuestions
          userQuestion={userQuestion}
          cards={cards}
          spreadType={spreadType}
        />
      )}

      {/* 📊 Rate This Reading Button - 在 New Reading 按钮上面 */}
      {showRateButton && readingId && onRateClick && (
        <motion.div
          className="mt-6 flex justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
        >
          <Button
            onClick={onRateClick}
            variant="outline"
            className="border-mystic-purple/30 hover:bg-mystic-purple/10"
          >
            <Star className="mr-2 h-4 w-4" />
            {language === 'en' ? 'Rate This Reading' : '评价这次占卜'}
          </Button>
        </motion.div>
      )}

      {/* New Reading Button */}
      {!loading && isComplete && (
        <NewReadingButton onNewReading={onNewReading} />
      )}
    </>
  );
};

export default ReadingDisplay;
