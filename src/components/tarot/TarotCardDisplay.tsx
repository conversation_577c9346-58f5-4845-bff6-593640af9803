
import React from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/contexts/LanguageContext';
import { readingDescriptions } from '@/data/readingDescriptions';

interface TarotCardDisplayProps {
  selectedCards: Array<{
    id: number;
    name: string;
    image: string;
    meaning: string;
    reversedMeaning: string;
    interpretation: string;
    position: "upright" | "reversed";
    rotationDegree: number;
  }>;
  spreadType: string;
}

const TarotCardDisplay: React.FC<TarotCardDisplayProps> = ({
  selectedCards,
  spreadType
}) => {
  const { t } = useLanguage();

  // Get spread info for positions
  const getSpreadInfo = () => {
    try {
      return readingDescriptions[spreadType as keyof typeof readingDescriptions] || {
        description: '',
        positions: Array(selectedCards.length).fill('')
      };
    } catch (error) {
      console.error("Error loading spread descriptions:", error);
      return { description: '', positions: Array(selectedCards.length).fill('') };
    }
  };

  const spreadInfo = getSpreadInfo();

  return (
    <div className="flex flex-wrap justify-center gap-4 md:gap-8 mb-12">
      {selectedCards.map((card, index) => (
        <motion.div
          key={card.id}
          className="flex flex-col items-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.2, duration: 0.5 }}
        >
          <div className="w-32 h-56 md:w-40 md:h-72 rounded-lg overflow-hidden shadow-xl mb-4">
            <div
              className="w-full h-full relative"
              style={{ transform: `rotate(${card.position === 'reversed' ? '180deg' : '0deg'})` }}
            >
              <img
                src={card.image}
                alt={card.name}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
          <h3 className="text-lg font-medium text-mystic-purple">{card.name}</h3>
          <p className="text-sm text-center text-mystic-gold mt-1">
            {t(card.position === 'upright' ? 'position.upright' : 'position.reversed')}
          </p>
          {spreadInfo?.positions[index] && (
            <p className="text-sm text-center text-muted-foreground mt-1">
              {spreadInfo.positions[index]}
            </p>
          )}
        </motion.div>
      ))}
    </div>
  );
};

export default TarotCardDisplay;
