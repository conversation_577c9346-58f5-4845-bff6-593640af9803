
import React from 'react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/LanguageContext';

interface NewReadingButtonProps {
  onNewReading: () => void;
}

const NewReadingButton: React.FC<NewReadingButtonProps> = ({ onNewReading }) => {
  const { t } = useLanguage();
  
  return (
    <div className="mt-8 flex justify-center">
      <Button
        variant="default"
        size="lg"
        className="bg-mystic-purple hover:bg-mystic-purple/90 text-white"
        onClick={onNewReading}
      >
        {t('reading.new')}
      </Button>
    </div>
  );
};

export default NewReadingButton;
