
import React, { useState, useEffect } from 'react';
import { Loader2, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useLanguage } from '@/contexts/LanguageContext';
import { generateFollowUpResponse } from '@/utils/aiReader';
import { useTypewriterEffect } from '@/hooks/useTypewriterEffect';
import { useNavigate } from 'react-router-dom';
import { toast } from "@/components/ui/use-toast";

interface FollowUpQuestionsProps {
  userQuestion: string;
  cards: Array<{name: string, position: "upright" | "reversed", meaning: string}>;
  spreadType: string;
}

const FollowUpQuestions: React.FC<FollowUpQuestionsProps> = ({
  userQuestion,
  cards,
  spreadType
}) => {
  const { language } = useLanguage();
  const navigate = useNavigate();
  const [followUpQuestion, setFollowUpQuestion] = useState('');
  const [followUpResponses, setFollowUpResponses] = useState<{question: string, answer: string}[]>([]);
  const [followUpCount, setFollowUpCount] = useState(0);
  const [isLoadingFollowUp, setIsLoadingFollowUp] = useState(false);
  const [showPromotion, setShowPromotion] = useState(false);

  // State to store current follow-up answer for typewriter effect
  const [currentFollowUpAnswer, setCurrentFollowUpAnswer] = useState('');
  const { displayedText: currentFollowUpText, isComplete: isFollowUpComplete, startTypewriter } = useTypewriterEffect(currentFollowUpAnswer);
  const [currentAnswerIndex, setCurrentAnswerIndex] = useState(-1);

  // Update the text for typewriter when the answer changes
  useEffect(() => {
    if (currentFollowUpAnswer) {
      startTypewriter();
    }
  }, [currentFollowUpAnswer, startTypewriter]);

  const handleFollowUpSubmit = async () => {
    if (!followUpQuestion.trim() || isLoadingFollowUp) return;

    setIsLoadingFollowUp(true);
    const newCount = followUpCount + 1;
    setFollowUpCount(newCount);

    // Prepare cards format for the API
    const cardsForReading = cards.map(card => ({
      name: card.name,
      position: card.position,
      meaning: card.meaning
    }));

    // Always use tarot context for all questions
    const response = await generateFollowUpResponse(
      userQuestion,
      followUpQuestion,
      cardsForReading,
      spreadType,
      language as 'en' | 'zh'
    );

    // Still show promotion after 3 questions
    if (newCount > 3 && !showPromotion) {
      setShowPromotion(true);
    }

    const newResponse = {
      question: followUpQuestion,
      answer: response
    };

    setFollowUpResponses(prev => [...prev, newResponse]);
    setFollowUpQuestion('');
    setIsLoadingFollowUp(false);
    setCurrentAnswerIndex(followUpResponses.length);

    // Update the text to trigger the typewriter effect
    setCurrentFollowUpAnswer(response);
  };

  const navigateToProfessional = () => {
    // 🔧 Professional页面已删除，改为跳转到Support页面
    navigate('/support');
    toast({
      title: language === 'en' ? "Contact Support" : "联系客服",
      description: language === 'en' ? "Get professional help via WeChat: zhidasuc" : "通过微信获得专业帮助：zhidasuc",
      variant: "default"
    });
  };

  return (
    <div className="mt-8 bg-card rounded-lg p-6 shadow-lg">
      <h3 className="text-xl font-bold text-mystic-purple mb-4">
        {language === 'zh' ? '追问解读' : 'Follow-up Questions'}
      </h3>

      {/* Display previous follow-up Q&As */}
      {followUpResponses.map((item, index) => (
        <div key={index} className="mb-6">
          <div className="bg-muted/50 p-3 rounded-lg mb-2">
            <p className="font-medium">
              {language === 'zh' ? '问题' : 'Question'}: {item.question}
            </p>
          </div>
          <div className="prose prose-sm max-w-none ml-4">
            {index === currentAnswerIndex ?
              (isFollowUpComplete ?
                currentFollowUpText.split("\n").map((para, idx) => (
                  para ? <p key={idx} className="mb-2">{para}</p> : null
                )) :
                <div className="flex items-center text-muted-foreground py-2">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  <span>{language === 'zh' ? '解读生成中，请在心中默默思考自己的问题～' : 'Interpretation in progress, please quietly contemplate your question in your heart~'}</span>
                </div>
              ) :
              item.answer.split("\n").map((para, idx) => (
                para ? <p key={idx} className="mb-2">{para}</p> : null
              ))
            }
          </div>
        </div>
      ))}

      {/* Promotion message after 3 follow-up questions */}
      {showPromotion && (
        <div className="bg-mystic-purple/10 p-4 rounded-lg mb-6 border border-mystic-purple/30">
          <p className="font-medium text-mystic-purple mb-2">
            {language === 'zh'
              ? '看来你对这个问题很感兴趣！'
              : 'You seem really interested in this topic!'}
          </p>
          <p className="mb-3">
            {language === 'zh'
              ? '需要更深入的解读？联系我们的专业塔罗师！'
              : 'Need deeper insights? Contact our professional tarot readers!'}
          </p>
          <Button
            onClick={navigateToProfessional}
            className="bg-mystic-purple hover:bg-mystic-purple/90 text-white"
          >
            {language === 'zh' ? '联系专业塔罗师' : 'Contact Professional Reader'}
          </Button>
        </div>
      )}

      {/* Input for new follow-up question */}
      <div className="mt-4">
        <div className="flex items-end gap-2">
          <Textarea
            placeholder={language === 'zh' ?
              '输入你的塔罗追问...' :
              'Enter your follow-up question about the reading...'}
            value={followUpQuestion}
            onChange={(e) => setFollowUpQuestion(e.target.value)}
            className="flex-1 resize-none"
            rows={2}
          />
          <Button
            onClick={handleFollowUpSubmit}
            disabled={!followUpQuestion.trim() || isLoadingFollowUp}
            size="icon"
            className={`bg-mystic-purple hover:bg-mystic-purple/90 text-white h-10 w-10 rounded-full flex items-center justify-center`}
          >
            {isLoadingFollowUp ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          {language === 'zh'
            ? '所有问题都将基于塔罗牌解读进行回答'
            : 'All questions will be answered based on your tarot reading'}
        </p>
      </div>
    </div>
  );
};

export default FollowUpQuestions;
