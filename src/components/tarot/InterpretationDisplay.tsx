
import React from 'react';
import { Loader2, Crown, Sparkles } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import ReadingLanguageToggle from './ReadingLanguageToggle';

interface InterpretationDisplayProps {
  loading: boolean;
  displayedText: string;
  // 🌐 翻译相关props
  showTranslateButton?: boolean;
  currentReading?: string;
  onTranslationComplete?: (translatedReading: string, newLanguage: 'en' | 'zh') => void;
  originalQuestion?: string;
  translationDisabled?: boolean;
  cards?: Array<{name: string, position: "upright" | "reversed", meaning: string}>;
}

const InterpretationDisplay: React.FC<InterpretationDisplayProps> = ({
  loading,
  displayedText,
  showTranslateButton,
  currentReading,
  onTranslationComplete,
  originalQuestion,
  translationDisabled,
  cards
}) => {
  const { t, language } = useLanguage();
  const navigate = useNavigate();

  // 检测是否是限制消息
  const isLimitMessage = displayedText && (
    displayedText.includes('您今天的免费问题次数已用完') ||
    displayedText.includes("You've reached your daily question limit") ||
    displayedText.includes('升级到高级会员') ||
    displayedText.includes('Upgrade to Premium')
  );

  const handleUpgradeClick = () => {
    navigate('/pricing');
  };

  return (
    <div className="bg-card rounded-lg p-6 shadow-lg min-h-40">
      {/* 标题和翻译按钮 */}
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-bold text-mystic-purple">{t('reading.interpretation')}</h3>

        {/* 🌐 翻译按钮 - 在标题右边 */}
        {showTranslateButton && currentReading && onTranslationComplete && (
          <ReadingLanguageToggle
            currentReading={currentReading}
            onTranslationComplete={onTranslationComplete}
            originalQuestion={originalQuestion}
            cards={cards?.map(card => ({
              name: card.name,
              position: card.position,
              meaning: card.position === 'upright' ? card.meaning : card.reversedMeaning || card.meaning
            }))}
            disabled={translationDisabled}
          />
        )}
      </div>

      {loading ? (
        <div className="flex flex-col items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-mystic-purple" />
          <p className="mt-4 text-muted-foreground">{t('reading.loading')}</p>
        </div>
      ) : (
        <div className="prose prose-lg max-w-none text-foreground">
          {displayedText ? (
            isLimitMessage ? (
              // 特殊的限制消息UI
              <div className="text-center py-8">
                <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-8 border border-purple-200 dark:border-purple-800">
                  <div className="flex justify-center mb-4">
                    <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-full p-3">
                      <Crown className="h-8 w-8 text-white" />
                    </div>
                  </div>

                  <div className="space-y-4 mb-6">
                    {displayedText.split("\n").map((paragraph, idx) => (
                      paragraph ? <p key={idx} className="text-gray-700 dark:text-gray-300">{paragraph}</p> : null
                    ))}
                  </div>

                  <Button
                    onClick={handleUpgradeClick}
                    className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-8 py-3 rounded-full font-semibold shadow-lg transform transition-all duration-200 hover:scale-105"
                  >
                    <Sparkles className="mr-2 h-5 w-5" />
                    {language === 'zh' ? '升级会员' : 'Upgrade Now'}
                  </Button>
                </div>
              </div>
            ) : (
              // 正常的解读内容
              displayedText.split("\n").map((paragraph, idx) => (
                paragraph ? <p key={idx} className="mb-4">{paragraph}</p> : null
              ))
            )
          ) : (
            <div className="text-center py-4">
              <Loader2 className="h-8 w-8 animate-spin text-mystic-purple mx-auto" />
              <p className="mt-4 text-muted-foreground">{t('reading.processing')}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default InterpretationDisplay;
