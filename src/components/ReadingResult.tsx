
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { readingDescriptions } from '@/data/readingDescriptions';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { generateTarotReading, generateTarotReadingStream } from '@/utils/aiReader';
import { useTypewriterEffect } from '@/hooks/useTypewriterEffect';
import { supabase } from '@/integrations/supabase/client';
import { toast } from "@/components/ui/use-toast";
import { Star } from 'lucide-react';
import FeedbackDialog from './FeedbackDialog';

// Import the component files
import TarotCardDisplay from './tarot/TarotCardDisplay';
import ReadingDisplay from './tarot/ReadingDisplay';
import ReadingHeader from './tarot/ReadingHeader';
import ReadingLanguageToggle from './tarot/ReadingLanguageToggle';

interface ReadingResultProps {
  selectedCards: Array<{
    id: number;
    name: string;
    image: string;
    meaning: string;
    reversedMeaning: string;
    interpretation: string;
    position: "upright" | "reversed";
    rotationDegree: number;
  }>;
  spreadType: string;
  userQuestion: string;
  onNewReading: () => void;
}

const ReadingResult: React.FC<ReadingResultProps> = ({
  selectedCards,
  spreadType,
  userQuestion,
  onNewReading
}) => {
  const { language } = useLanguage();
  const { user } = useAuth();
  const { checkUsageLimit, incrementQuestionCount, refreshSubscription } = useSubscription();
  const [aiReading, setAiReading] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [readingId, setReadingId] = useState<string | null>(null);
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  // 不再使用打字机效果，直接显示流式内容
  const [isComplete, setIsComplete] = useState(false);
  // 🌐 翻译相关状态
  const [currentReadingLanguage, setCurrentReadingLanguage] = useState<'en' | 'zh'>(language as 'en' | 'zh');
  const [originalReading, setOriginalReading] = useState<string>(''); // 保存原始解读

  useEffect(() => {
    // 如果已经有完整的解读内容，不重新生成
    if (aiReading && aiReading.length > 100 && !aiReading.includes('Error:') && !aiReading.includes('错误：') && !aiReading.includes('很抱歉')) {
      console.log("Reading already exists, skipping regeneration");
      return;
    }

    const getReading = async () => {
      // 防止重复调用
      if (isGenerating) {
        console.log("Already generating, skipping...");
        return;
      }

      try {
        setIsGenerating(true);
        setLoading(true);
        console.log("=== Starting new reading generation ===");
        console.log("Getting reading for cards:", selectedCards);

        // Make sure we have valid cards data
        if (!selectedCards || selectedCards.length === 0) {
          console.error("No cards selected");
          setAiReading(language === 'en'
            ? "Error: No cards were selected for reading. Please try again."
            : "错误：未选择卡牌进行解读。请重试。");
          setLoading(false);
          return;
        }

        // Prepare cards for reading
        const cardsForReading = selectedCards.map(card => ({
          name: card.name,
          position: card.position,
          meaning: card.position === 'upright' ? card.meaning : card.reversedMeaning
        }));

        console.log("Prepared cards for reading:", cardsForReading);
        console.log("User question:", userQuestion);
        console.log("Spread type:", spreadType);
        console.log("checkUsageLimit function:", typeof checkUsageLimit);
        console.log("incrementQuestionCount function:", typeof incrementQuestionCount);

        // 立即开始显示，不等待完整响应
        setLoading(false);

        // 使用流式API生成解读，实时显示内容
        const reading = await generateTarotReadingStream(
          userQuestion || (language === 'en' ? "What should I focus on right now?" : "我现在应该关注什么?"),
          cardsForReading,
          spreadType,
          language as 'en' | 'zh',
          checkUsageLimit,
          incrementQuestionCount,
          // 实时更新回调函数
          (chunk: string) => {
            console.log("📝 Streaming chunk received, length:", chunk.length);
            setAiReading(chunk);
          }
        );

        console.log("✅ Final reading generated:", reading.length, "characters");

        // 确保最终内容被设置
        setAiReading(reading);
        setOriginalReading(reading); // 保存原始解读
        setCurrentReadingLanguage(language as 'en' | 'zh'); // 设置当前解读语言
        setIsComplete(true);

        // 🔧 不需要刷新订阅状态，因为incrementQuestionCount已经更新了本地状态
        console.log("✅ Question count already updated by incrementQuestionCount, no need to refresh");

        // Save reading to database if user is logged in
        if (user) {
          try {
            console.log("💾 Saving reading to reading_history table...");
            const { data, error } = await supabase
              .from('reading_history')
              .insert({
                user_id: user.id,
                question: userQuestion || (language === 'en' ? "What should I focus on right now?" : "我现在应该关注什么?"),
                spread_type: spreadType,
                cards: selectedCards,
                ai_reading: reading
              })
              .select()
              .single();

            if (error) throw error;
            console.log("✅ Reading saved to reading_history with ID:", data.id);
            setReadingId(data.id);
          } catch (error) {
            console.error("Error saving reading:", error);
          }
        }

        setLoading(false);
        setIsGenerating(false);

      } catch (error) {
        console.error("Error getting reading:", error);
        setAiReading(language === 'en'
          ? "I'm sorry, but I couldn't generate a reading at this time. Please try again later."
          : "很抱歉，我目前无法生成解读。请稍后再试。");
        setLoading(false);
        setIsGenerating(false);
        toast({
          title: language === 'en' ? "Error" : "错误",
          description: language === 'en' ? "Failed to generate reading" : "无法生成解读",
          variant: "destructive"
        });
      }
    };

    getReading();
  }, [selectedCards, spreadType, userQuestion, language]); // 移除 user 依赖以防止重复生成

  // 🌐 处理翻译完成
  const handleTranslationComplete = (translatedReading: string, newLanguage: 'en' | 'zh') => {
    setAiReading(translatedReading);
    setCurrentReadingLanguage(newLanguage);
    console.log(`✅ Reading translated to ${newLanguage}`);
  };

  return (
    <motion.div
      className="w-full max-w-4xl mx-auto p-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <ReadingHeader
        spreadType={spreadType}
        userQuestion={userQuestion}
      />

      <TarotCardDisplay
        selectedCards={selectedCards}
        spreadType={spreadType}
      />

      <Separator className="my-8 bg-mystic-purple/20" />

      <ReadingDisplay
        loading={loading}
        displayedText={aiReading}
        isComplete={isComplete}
        onNewReading={onNewReading}
        cards={selectedCards}
        spreadType={spreadType}
        userQuestion={userQuestion}
        // 🌐 传递翻译相关props
        showTranslateButton={isComplete && aiReading && !loading}
        currentReading={aiReading}
        onTranslationComplete={handleTranslationComplete}
        originalQuestion={userQuestion}
        translationDisabled={isGenerating}
        // 📊 传递评分相关props
        showRateButton={user && isComplete && readingId}
        readingId={readingId}
        onRateClick={() => setFeedbackDialogOpen(true)}
      />

      {/* 📊 评分按钮已移动到ReadingDisplay组件中，在New Reading按钮上面 */}

      {/* 反馈对话框 */}
      {readingId && (
        <FeedbackDialog
          open={feedbackDialogOpen}
          onOpenChange={setFeedbackDialogOpen}
          readingId={readingId}
        />
      )}
    </motion.div>
  );
};

export default ReadingResult;
