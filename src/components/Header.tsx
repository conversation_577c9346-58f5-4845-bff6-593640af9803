
import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Moon, Sun } from 'lucide-react';
import { Link } from 'react-router-dom';

interface HeaderProps {
  onNewReading?: () => void;
}

const Header: React.FC<HeaderProps> = ({ onNewReading }) => {
  return (
    <header className="w-full py-4 px-6">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        <motion.div
          className="flex items-center"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Link to="/" className="flex items-center gap-2">
            <span className="text-3xl text-mystic-gold">✧</span>
            <span className="text-xl font-bold text-mystic-purple">Mystic Mirror</span>
          </Link>
        </motion.div>
        
        <motion.div
          className="flex items-center gap-4"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          {onNewReading && (
            <Button
              variant="outline"
              className="border-mystic-purple text-mystic-purple"
              onClick={onNewReading}
            >
              New Reading
            </Button>
          )}
        </motion.div>
      </div>
    </header>
  );
};

export default Header;
