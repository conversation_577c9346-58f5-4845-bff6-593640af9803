
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import TarotCard from './TarotCard';
import { tarotCards } from '@/data/tarotCards';

interface CardDeckProps {
  spreadType: string;
  maxCards: number;
  onReadingComplete: (selectedCards: any[]) => void;
}

const CardDeck: React.FC<CardDeckProps> = ({ spreadType, maxCards, onReadingComplete }) => {
  const [selectedCards, setSelectedCards] = useState<number[]>([]);
  const [flippedCards, setFlippedCards] = useState<number[]>([]);
  const [shuffledCards, setShuffledCards] = useState(() => {
    // Get random cards from the deck
    return [...tarotCards]
      .sort(() => Math.random() - 0.5)
      .slice(0, maxCards)
      .map(card => {
        // Random upright or reversed position
        const position = Math.random() > 0.5 ? "upright" : "reversed";
        return {
          ...card,
          position,
          rotationDegree: position === "upright" ? 0 : 180
        };
      });
  });

  // Auto-select cards with a delay for animation
  useEffect(() => {
    const selectCards = async () => {
      for (let i = 0; i < shuffledCards.length; i++) {
        const card = shuffledCards[i];
        await new Promise(resolve => setTimeout(resolve, 500));
        setSelectedCards(prev => [...prev, card.id]);
        await new Promise(resolve => setTimeout(resolve, 300));
        setFlippedCards(prev => [...prev, card.id]);
      }
      
      setTimeout(() => {
        console.log("Reading complete, selected cards:", shuffledCards);
        onReadingComplete(shuffledCards);
      }, 1000);
    };
    
    selectCards();
  }, [shuffledCards, onReadingComplete]);

  return (
    <div className="flex flex-col items-center w-full">
      <motion.h2 
        className="text-2xl md:text-3xl font-bold text-mystic-purple mb-6"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {spreadType} Spread
      </motion.h2>
      
      <motion.p 
        className="text-center text-muted-foreground mb-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        Drawing {maxCards} card{maxCards > 1 ? 's' : ''} for your reading...
      </motion.p>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 md:gap-6 w-full max-w-5xl mb-8">
        {shuffledCards.map((card, index) => (
          <motion.div
            key={card.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05, duration: 0.3 }}
          >
            <TarotCard
              id={card.id}
              name={card.name}
              image={card.image}
              isFlipped={flippedCards.includes(card.id)}
              isSelected={selectedCards.includes(card.id)}
              onClick={() => {}}
              rotationDegree={card.position === 'reversed' ? 180 : 0}
            />
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default CardDeck;
