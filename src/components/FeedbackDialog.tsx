
import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { supabase } from '@/integrations/supabase/client';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/use-toast';
import { Star } from 'lucide-react';

interface FeedbackDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  readingId: string;
}

const FeedbackDialog: React.FC<FeedbackDialogProps> = ({
  open,
  onOpenChange,
  readingId
}) => {
  const { user } = useAuth();
  const { language } = useLanguage();
  const [ratings, setRatings] = useState({
    accuracy: 0,
    satisfaction: 0,
    usefulness: 0
  });
  const [feedbackText, setFeedbackText] = useState('');
  const [loading, setLoading] = useState(false);

  const handleStarClick = (category: keyof typeof ratings, rating: number) => {
    setRatings(prev => ({
      ...prev,
      [category]: rating
    }));
  };

  const handleSubmit = async () => {
    if (!user) {
      console.error("❌ No user found");
      return;
    }

    if (!readingId) {
      console.error("❌ No reading ID provided");
      toast({
        title: language === 'en' ? 'Error' : '错误',
        description: language === 'en' ? 'Reading ID is missing' : '缺少占卜ID',
        variant: 'destructive'
      });
      return;
    }

    setLoading(true);
    try {
      console.log("💬 Starting feedback save process...");
      console.log("📖 Reading ID:", readingId);
      console.log("👤 User ID:", user.id);
      console.log("⭐ Ratings:", ratings);

      // 首先测试表格访问权限
      console.log("🔍 Testing table access...");
      const { data: testData, error: testError } = await supabase
        .from('reading_feedback')
        .select('count')
        .limit(1);

      if (testError) {
        console.error("❌ Table access test failed:", testError);
        throw new Error(`Table access denied: ${testError.message}`);
      }

      console.log("✅ Table access test passed");

      // 验证必要的数据
      console.log("🔍 Validating data before save:");
      console.log("📖 Reading ID:", readingId);
      console.log("👤 User ID:", user.id);
      console.log("⭐ Accuracy rating:", ratings.accuracy);
      console.log("⭐ Satisfaction rating:", ratings.satisfaction);
      console.log("⭐ Usefulness rating:", ratings.usefulness);
      console.log("💬 Feedback text length:", feedbackText.length);

      // 首先验证reading_id是否存在于reading_history表中
      console.log("🔍 Verifying reading_id exists in reading_history...");
      console.log("🔍 Reading ID to verify:", readingId);

      const { data: readingExists, error: readingCheckError } = await supabase
        .from('reading_history')
        .select('id')
        .eq('id', readingId)
        .single();

      console.log("🔍 Reading verification result:", { readingExists, readingCheckError });

      if (readingCheckError) {
        console.error("❌ Error checking reading_history:", readingCheckError);
        if (readingCheckError.code === 'PGRST116') {
          console.error("❌ Reading ID not found in reading_history table!");
          console.error("❌ This means the reading was not saved properly");
          throw new Error(`Reading ID ${readingId} does not exist in reading_history table`);
        }
        throw readingCheckError;
      }

      if (!readingExists) {
        console.error("❌ Reading ID not found in reading_history (no data returned)");
        throw new Error(`Reading ID ${readingId} does not exist in reading_history table`);
      }

      console.log("✅ Reading ID verified in reading_history table:", readingExists.id);

      // 先检查是否已存在反馈（不使用single()避免错误）
      console.log("🔍 Checking for existing feedback...");
      const { data: existingFeedbackList, error: checkError } = await supabase
        .from('reading_feedback')
        .select('id')
        .eq('reading_id', readingId)
        .eq('user_id', user.id);

      if (checkError) {
        console.error("❌ Error checking existing feedback:", checkError);
        throw checkError;
      }

      const existingFeedback = existingFeedbackList && existingFeedbackList.length > 0 ? existingFeedbackList[0] : null;
      console.log("🔍 Existing feedback found:", !!existingFeedback);
      console.log("🔍 Existing feedback ID:", existingFeedback?.id);

      let result;
      if (existingFeedback) {
        // 更新现有反馈
        console.log("📝 Updating existing feedback with ID:", existingFeedback.id);
        result = await supabase
          .from('reading_feedback')
          .update({
            accuracy_rating: ratings.accuracy || null,
            satisfaction_rating: ratings.satisfaction || null,
            usefulness_rating: ratings.usefulness || null,
            feedback_text: feedbackText || null
          })
          .eq('id', existingFeedback.id)
          .select();
      } else {
        // 创建新反馈 - 使用upsert避免409冲突
        console.log("✨ Creating new feedback entry");

        const feedbackData = {
          reading_id: readingId,
          user_id: user.id,
          accuracy_rating: ratings.accuracy || null,
          satisfaction_rating: ratings.satisfaction || null,
          usefulness_rating: ratings.usefulness || null,
          feedback_text: feedbackText || null
        };

        console.log("📝 Feedback data to insert:", feedbackData);

        result = await supabase
          .from('reading_feedback')
          .upsert(feedbackData, {
            onConflict: 'reading_id,user_id'
          })
          .select();
      }

      console.log("📊 Database operation result:", result);

      if (result.error) {
        console.error("❌ Feedback save error:", result.error);
        console.error("❌ Error code:", result.error.code);
        console.error("❌ Error message:", result.error.message);
        console.error("❌ Error details:", result.error.details);

        // 特别处理外键约束错误
        if (result.error.code === '23503') {
          console.error("❌ Foreign key constraint violation!");
          console.error("❌ This means reading_id does not exist in the referenced table");
          console.error("❌ Reading ID:", readingId);
          console.error("❌ Check if the reading was saved properly to reading_history table");
        }

        throw result.error;
      }

      console.log("✅ Feedback saved successfully");

      toast({
        title: language === 'en' ? 'Thank you!' : '谢谢！',
        description: language === 'en' ? 'Your feedback has been saved' : '您的反馈已保存'
      });

      onOpenChange(false);

      // 重置表单
      setRatings({ accuracy: 0, satisfaction: 0, usefulness: 0 });
      setFeedbackText('');
    } catch (error) {
      console.error("❌ Final catch block - Feedback save error:", error);
      console.error("❌ Error type:", typeof error);
      console.error("❌ Error message:", error?.message);
      console.error("❌ Error code:", error?.code);
      console.error("❌ Error details:", error?.details);
      console.error("❌ Full error object:", JSON.stringify(error, null, 2));

      let errorMessage = language === 'en' ? 'Failed to save feedback' : '保存反馈失败';

      if (error?.message) {
        errorMessage += `: ${error.message}`;
      }

      if (error?.code === '23503') {
        errorMessage = language === 'en'
          ? 'Reading not found. Please complete a new tarot reading first.'
          : '找不到占卜记录。请先完成一次新的塔罗占卜。';
      }

      toast({
        title: language === 'en' ? 'Error' : '错误',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const renderStarRating = (category: keyof typeof ratings, label: string) => {
    return (
      <div className="space-y-2">
        <label className="text-sm font-medium">{label}</label>
        <div className="flex gap-1">
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              type="button"
              onClick={() => handleStarClick(category, star)}
              className="transition-colors"
            >
              <Star
                className={`h-6 w-6 ${
                  star <= ratings[category]
                    ? 'text-yellow-400 fill-current'
                    : 'text-gray-300 hover:text-yellow-200'
                }`}
              />
            </button>
          ))}
        </div>
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-mystic-purple">
            {language === 'en' ? 'Rate Your Reading' : '评价您的占卜'}
          </DialogTitle>
          <DialogDescription>
            {language === 'en'
              ? 'Please share your thoughts about this tarot reading'
              : '请分享您对这次塔罗占卜的想法'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {renderStarRating('accuracy', language === 'en' ? 'Accuracy' : '准确度')}
          {renderStarRating('satisfaction', language === 'en' ? 'Satisfaction' : '满意度')}
          {renderStarRating('usefulness', language === 'en' ? 'Usefulness' : '有用度')}

          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'en' ? 'Additional Feedback (Optional)' : '额外反馈（可选）'}
            </label>
            <Textarea
              placeholder={language === 'en'
                ? 'Share your thoughts, suggestions, or experiences...'
                : '分享您的想法、建议或体验...'
              }
              value={feedbackText}
              onChange={(e) => setFeedbackText(e.target.value)}
              className="min-h-[100px] bg-background border-mystic-purple/30"
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="border-mystic-purple/30"
          >
            {language === 'en' ? 'Cancel' : '取消'}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading}
            className="bg-mystic-purple hover:bg-mystic-purple/90"
          >
            {loading
              ? (language === 'en' ? 'Saving...' : '保存中...')
              : (language === 'en' ? 'Save Feedback' : '保存反馈')
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FeedbackDialog;
