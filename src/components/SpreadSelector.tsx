
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useLanguage } from '@/contexts/LanguageContext';

interface SpreadOption {
  id: string;
  name: string;
  description: string;
  cardCount: number;
  icon: string;
}

interface SpreadSelectorProps {
  onSelectSpread: (spreadType: string, cardCount: number, question: string) => void;
}

const SpreadSelector: React.FC<SpreadSelectorProps> = ({ onSelectSpread }) => {
  const [question, setQuestion] = useState('');
  const { t, language } = useLanguage();

  const spreadOptions: SpreadOption[] = [
    {
      id: 'single',
      name: language === 'en' ? 'Single Card' : '单卡解读',
      description: language === 'en' ? 'A quick reading for daily guidance or a specific question' : '针对每日指导或特定问题的快速解读',
      cardCount: 1,
      icon: '★'
    },
    {
      id: 'three-card',
      name: language === 'en' ? 'Three Card' : '三卡解读',
      description: language === 'en' ? 'Past, Present and Future or Mind, Body and Spirit' : '过去、现在和未来，或心灵、身体和精神',
      cardCount: 3,
      icon: '⁂'
    },
    {
      id: 'celtic-cross',
      name: language === 'en' ? 'Celtic Cross' : '凯尔特十字',
      description: language === 'en' ? 'A comprehensive 6-card spread for deeper insights' : '一个全面的6卡牌阵，提供更深入的洞察',
      cardCount: 6,
      icon: '✧'
    }
  ];

  const handleSubmit = (spreadId: string, cardCount: number) => {
    if (!question.trim()) {
      alert(language === 'en' ? "Please enter your question first" : "请先输入您的问题");
      return;
    }
    onSelectSpread(spreadId, cardCount, question);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <motion.div 
        className="text-center mb-12"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h2 className="text-3xl md:text-4xl font-bold text-mystic-purple mb-4">
          {language === 'en' ? 'What would you like to know?' : '你想知道什么？'}
        </h2>
        <p className="text-lg text-muted-foreground mb-6">
          {language === 'en' ? 'Enter your question below and select a spread type' : '在下方输入您的问题并选择一种牌阵类型'}
        </p>
        
        <div className="max-w-2xl mx-auto mb-12">
          <Textarea 
            value={question}
            onChange={(e) => setQuestion(e.target.value)}
            placeholder={language === 'en' ? "Enter your question here..." : "在此输入您的问题..."}
            className="w-full h-32 p-4 text-lg bg-mystic-dark/20 border-mystic-purple/30"
          />
        </div>
      </motion.div>
      
      <motion.div
        className="text-center mb-8"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <h3 className="text-2xl font-bold text-mystic-purple mb-4">
          {language === 'en' ? 'Choose Your Tarot Spread' : '选择您的塔罗牌阵'}
        </h3>
      </motion.div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {spreadOptions.map((spread, index) => (
          <motion.div
            key={spread.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1, duration: 0.5 }}
          >
            <Card className="bg-card border-mystic-purple/20 shadow-lg h-full">
              <CardHeader>
                <div className="text-3xl text-center text-mystic-gold mb-4">{spread.icon}</div>
                <CardTitle className="text-xl text-center text-mystic-purple">{spread.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center text-muted-foreground">
                  {spread.description}
                </CardDescription>
              </CardContent>
              <CardFooter className="flex justify-center">
                <Button 
                  variant="default" 
                  className="bg-mystic-purple hover:bg-mystic-purple/90 text-white"
                  onClick={() => handleSubmit(spread.id, spread.cardCount)}
                >
                  {language === 'en' ? 'Select Spread' : '选择牌阵'}
                </Button>
              </CardFooter>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default SpreadSelector;
