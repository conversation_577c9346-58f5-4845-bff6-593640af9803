
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface TarotCardProps {
  id: number;
  name: string;
  image: string;
  isFlipped: boolean;
  isSelected: boolean;
  onClick: () => void;
  className?: string;
  rotationDegree?: number;
}

const TarotCard: React.FC<TarotCardProps> = ({
  id,
  name,
  image,
  isFlipped,
  isSelected,
  onClick,
  className,
  rotationDegree = 0
}) => {
  const [isHovered, setIsHovered] = useState(false);
  
  // Use the actual image URL instead of the broken local path
  const cardBackUrl = "https://images.unsplash.com/photo-1574790338427-b10b4da146f5?q=80&w=1374&auto=format&fit=crop";
  
  return (
    <motion.div
      className={cn(
        "relative w-32 h-56 md:w-40 md:h-72 cursor-pointer",
        isSelected && "ring-4 ring-mystic-gold",
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ 
        opacity: 1, 
        y: 0,
        scale: isHovered && !isFlipped ? 1.05 : 1,
        rotateY: isFlipped ? 180 : 0,
        rotateZ: isFlipped ? rotationDegree : 0
      }}
      transition={{ 
        duration: 0.5,
        type: "spring",
        stiffness: 100
      }}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ transformStyle: "preserve-3d" }}
    >
      {/* Card Back */}
      <motion.div
        className="absolute inset-0 rounded-lg bg-card-gradient border border-mystic-purple shadow-lg"
        style={{ 
          backfaceVisibility: "hidden",
          backgroundImage: `url(${cardBackUrl})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
         }}
      >
        <div className="absolute inset-0 bg-mystic-dark/60 rounded-lg flex items-center justify-center">
          <div className="h-20 w-20 bg-mystic-gold/20 rounded-full flex items-center justify-center">
            <span className="text-mystic-gold text-4xl">♦</span>
          </div>
        </div>
      </motion.div>

      {/* Card Front */}
      <motion.div
        className="absolute inset-0 rounded-lg border border-mystic-purple overflow-hidden shadow-lg"
        style={{ 
          backfaceVisibility: "hidden",
          rotateY: 180,
          backgroundImage: `url(${image})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="absolute bottom-0 inset-x-0 bg-gradient-to-t from-mystic-dark/90 to-transparent p-2 text-center">
          <p className="text-white text-xs md:text-sm font-medium">{name}</p>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default TarotCard;
