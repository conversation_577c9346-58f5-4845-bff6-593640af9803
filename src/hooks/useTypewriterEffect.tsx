
import { useState, useEffect, useCallback } from 'react';

/**
 * A hook that provides a typewriter effect for text display
 *
 * @param text - The complete text to be displayed
 * @param speed - The typing speed in milliseconds per character (default: 20ms for faster display)
 * @returns Object containing displayed text, completion status, and start function
 */
export const useTypewriterEffect = (text: string, speed: number = 15) => {
  const [displayedText, setDisplayedText] = useState("");
  const [isComplete, setIsComplete] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [currentText, setCurrentText] = useState(text);

  // Update the current text when the input text changes
  useEffect(() => {
    setCurrentText(text);
  }, [text]);

  const startTypewriter = useCallback(() => {
    // Reset state if restarting
    setDisplayedText("");
    setCurrentIndex(0);
    setIsComplete(false);

    if (!currentText) {
      setIsComplete(true);
      return;
    }

    // Clear any existing intervals
    let intervalId: number;

    intervalId = window.setInterval(() => {
      setCurrentIndex(prevIndex => {
        const newIndex = prevIndex + 1;
        if (newIndex <= currentText.length) {
          setDisplayedText(currentText.substring(0, newIndex));
          return newIndex;
        } else {
          clearInterval(intervalId);
          setIsComplete(true);
          return prevIndex;
        }
      });
    }, speed);

    // Cleanup function to clear the interval when component unmounts or when startTypewriter is called again
    return () => {
      clearInterval(intervalId);
    };
  }, [currentText, speed]);

  return { displayedText, isComplete, startTypewriter };
};

export default useTypewriterEffect;
