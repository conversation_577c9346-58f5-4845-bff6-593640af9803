// Webhooks处理端点
// 这个文件需要在后端实现，这里提供前端的接口定义和处理逻辑

import { supabase } from '@/integrations/supabase/client';

export interface WebhookEvent {
  id: string;
  type: string;
  data: {
    object: any;
  };
  created: number;
}

// Webhook事件类型
export enum WebhookEventType {
  CHECKOUT_SESSION_COMPLETED = 'checkout.session.completed',
  CHECKOUT_SESSION_FAILED = 'checkout.session.failed',
  SUBSCRIPTION_CREATED = 'subscription.created',
  SUBSCRIPTION_UPDATED = 'subscription.updated',
  SUBSCRIPTION_CANCELLED = 'subscription.cancelled',
  PAYMENT_SUCCEEDED = 'payment.succeeded',
  PAYMENT_FAILED = 'payment.failed'
}

// 处理支付成功的Webhook
export const handlePaymentSuccess = async (sessionData: any) => {
  try {
    const userId = sessionData.metadata?.user_id;
    const subscriptionType = sessionData.metadata?.subscription_type || 'monthly';

    if (!userId) {
      console.error('No user ID found in session metadata');
      return { success: false, error: 'Missing user ID' };
    }

    // 计算订阅结束时间
    const subscriptionStart = new Date();
    const subscriptionEnd = new Date();
    subscriptionEnd.setMonth(subscriptionEnd.getMonth() + 1); // 1个月订阅

    // 更新用户订阅状态
    const { data, error } = await supabase
      .from('user_subscriptions')
      .update({
        subscription_type: subscriptionType,
        status: 'active',
        subscription_start_date: subscriptionStart.toISOString(),
        subscription_end_date: subscriptionEnd.toISOString(),
        daily_questions_used: 0,
        last_question_date: null,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error updating subscription:', error);
      return { success: false, error: error.message };
    }

    // 记录支付历史
    await supabase
      .from('payment_history')
      .insert({
        user_id: userId,
        session_id: sessionData.id,
        amount: sessionData.amount_total / 100, // 转换为美元
        currency: sessionData.currency,
        status: 'completed',
        payment_method: sessionData.payment_method_types?.[0] || 'unknown',
        created_at: new Date().toISOString()
      });

    console.log('Payment processed successfully for user:', userId);
    return { success: true, data };
  } catch (error) {
    console.error('Error handling payment success:', error);
    return { success: false, error: error.message };
  }
};

// 处理支付失败的Webhook
export const handlePaymentFailure = async (sessionData: any) => {
  try {
    const userId = sessionData.metadata?.user_id;

    if (!userId) {
      console.error('No user ID found in session metadata');
      return { success: false, error: 'Missing user ID' };
    }

    // 记录失败的支付尝试
    await supabase
      .from('payment_history')
      .insert({
        user_id: userId,
        session_id: sessionData.id,
        amount: sessionData.amount_total / 100,
        currency: sessionData.currency,
        status: 'failed',
        payment_method: sessionData.payment_method_types?.[0] || 'unknown',
        failure_reason: sessionData.failure_reason || 'Unknown error',
        created_at: new Date().toISOString()
      });

    console.log('Payment failure recorded for user:', userId);
    return { success: true };
  } catch (error) {
    console.error('Error handling payment failure:', error);
    return { success: false, error: error.message };
  }
};

// 处理订阅取消的Webhook
export const handleSubscriptionCancellation = async (subscriptionData: any) => {
  try {
    const userId = subscriptionData.metadata?.user_id;

    if (!userId) {
      console.error('No user ID found in subscription metadata');
      return { success: false, error: 'Missing user ID' };
    }

    // 更新订阅状态为已取消
    const { data, error } = await supabase
      .from('user_subscriptions')
      .update({
        status: 'cancelled',
        subscription_end_date: new Date().toISOString(), // 立即结束
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error cancelling subscription:', error);
      return { success: false, error: error.message };
    }

    console.log('Subscription cancelled for user:', userId);
    return { success: true, data };
  } catch (error) {
    console.error('Error handling subscription cancellation:', error);
    return { success: false, error: error.message };
  }
};

// 主要的Webhook处理函数
export const processWebhookEvent = async (event: WebhookEvent) => {
  try {
    console.log('Processing webhook event:', event.type);

    switch (event.type) {
      case WebhookEventType.CHECKOUT_SESSION_COMPLETED:
        return await handlePaymentSuccess(event.data.object);

      case WebhookEventType.CHECKOUT_SESSION_FAILED:
        return await handlePaymentFailure(event.data.object);

      case WebhookEventType.SUBSCRIPTION_CANCELLED:
        return await handleSubscriptionCancellation(event.data.object);

      case WebhookEventType.PAYMENT_SUCCEEDED:
        // 处理成功的支付
        console.log('Payment succeeded:', event.data.object);
        return { success: true };

      case WebhookEventType.PAYMENT_FAILED:
        // 处理失败的支付
        console.log('Payment failed:', event.data.object);
        return { success: true };

      default:
        console.log('Unhandled webhook event type:', event.type);
        return { success: true, message: 'Event type not handled' };
    }
  } catch (error) {
    console.error('Error processing webhook event:', error);
    return { success: false, error: error.message };
  }
};

// 验证Webhook签名（安全性）
export const verifyWebhookSignature = (
  payload: string,
  signature: string,
  secret: string
): boolean => {
  try {
    // 这里应该实现实际的签名验证逻辑
    // 通常使用HMAC-SHA256验证
    // 由于这是前端代码，实际的验证应该在后端进行

    // 简单的示例验证（实际应用中需要更安全的实现）
    const expectedSignature = `sha256=${signature}`;
    return signature === expectedSignature;
  } catch (error) {
    console.error('Error verifying webhook signature:', error);
    return false;
  }
};

// Webhook端点URL生成器
export const getWebhookEndpointUrl = (): string => {
  const baseUrl = process.env.NODE_ENV === 'production'
    ? 'https://mystic-card-mirror-vip.lovable.app'
    : 'http://localhost:8080';

  return `${baseUrl}/api/webhooks/creem`;
};

// 用于Creem.io配置的Webhook信息
export const getWebhookConfig = () => {
  return {
    url: getWebhookEndpointUrl(),
    events: [
      WebhookEventType.CHECKOUT_SESSION_COMPLETED,
      WebhookEventType.CHECKOUT_SESSION_FAILED,
      WebhookEventType.SUBSCRIPTION_CANCELLED,
      WebhookEventType.PAYMENT_SUCCEEDED,
      WebhookEventType.PAYMENT_FAILED
    ],
    description: 'Mystic Card Mirror - Payment and Subscription Events'
  };
};

// 导出Webhook配置信息供用户复制到Creem.io
export const WEBHOOK_ENDPOINT_INFO = {
  url: getWebhookEndpointUrl(),
  method: 'POST',
  contentType: 'application/json',
  events: [
    'checkout.session.completed',
    'checkout.session.failed',
    'subscription.cancelled',
    'payment.succeeded',
    'payment.failed'
  ],
  description: 'Copy this URL to your Creem.io webhook settings'
};
