// Webhook处理器 - 用于接收Creem.io事件
// 注意：这个文件需要在后端实现，这里提供前端的接口定义

export interface CreemWebhookEvent {
  id: string;
  type: string;
  data: {
    object: {
      id: string;
      status?: string;
      customer?: {
        id: string;
        email: string;
      };
      subscription?: {
        id: string;
        status: string;
      };
      order?: {
        id: string;
        amount: number;
        currency: string;
      };
      metadata?: {
        user_id?: string;
        subscription_type?: string;
      };
      request_id?: string;
    };
  };
  created_at: string;
}

// Creem.io事件类型
export enum CreemEventType {
  ORDER_CREATED = 'order.created',
  ORDER_COMPLETED = 'order.completed',
  ORDER_FAILED = 'order.failed',
  SUBSCRIPTION_CREATED = 'subscription.created',
  SUBSCRIPTION_UPDATED = 'subscription.updated',
  SUBSCRIPTION_CANCELLED = 'subscription.cancelled',
  SUBSCRIPTION_EXPIRED = 'subscription.expired',
  CUSTOMER_CREATED = 'customer.created',
  CUSTOMER_UPDATED = 'customer.updated'
}

// 处理订单完成事件
export const handleOrderCompleted = async (eventData: any) => {
  try {
    const { object } = eventData;
    const userId = object.request_id || object.metadata?.user_id;
    
    if (!userId) {
      console.error('No user ID found in order completed event');
      return { success: false, error: 'Missing user ID' };
    }

    console.log('Processing order completed for user:', userId);
    console.log('Order details:', object);

    // 这里应该调用Supabase来更新用户订阅状态
    // 由于这是前端代码，实际的数据库更新应该在后端进行
    
    // 前端可以通过localStorage或其他方式通知用户界面
    if (typeof window !== 'undefined') {
      localStorage.setItem('payment_completed', 'true');
      localStorage.setItem('order_id', object.id);
      localStorage.setItem('user_id', userId);
    }

    return { success: true };
  } catch (error) {
    console.error('Error handling order completed:', error);
    return { success: false, error: error.message };
  }
};

// 处理订阅创建事件
export const handleSubscriptionCreated = async (eventData: any) => {
  try {
    const { object } = eventData;
    const userId = object.request_id || object.metadata?.user_id;
    
    if (!userId) {
      console.error('No user ID found in subscription created event');
      return { success: false, error: 'Missing user ID' };
    }

    console.log('Processing subscription created for user:', userId);
    console.log('Subscription details:', object);

    // 更新用户订阅状态为active
    if (typeof window !== 'undefined') {
      localStorage.setItem('subscription_created', 'true');
      localStorage.setItem('subscription_id', object.id);
      localStorage.setItem('user_id', userId);
    }

    return { success: true };
  } catch (error) {
    console.error('Error handling subscription created:', error);
    return { success: false, error: error.message };
  }
};

// 处理订阅取消事件
export const handleSubscriptionCancelled = async (eventData: any) => {
  try {
    const { object } = eventData;
    const userId = object.request_id || object.metadata?.user_id;
    
    if (!userId) {
      console.error('No user ID found in subscription cancelled event');
      return { success: false, error: 'Missing user ID' };
    }

    console.log('Processing subscription cancelled for user:', userId);

    // 更新用户订阅状态为cancelled
    if (typeof window !== 'undefined') {
      localStorage.setItem('subscription_cancelled', 'true');
      localStorage.setItem('cancelled_subscription_id', object.id);
      localStorage.setItem('user_id', userId);
    }

    return { success: true };
  } catch (error) {
    console.error('Error handling subscription cancelled:', error);
    return { success: false, error: error.message };
  }
};

// 主要的Webhook处理函数
export const processCreemWebhook = async (event: CreemWebhookEvent) => {
  try {
    console.log('Processing Creem webhook event:', event.type);
    console.log('Event data:', event.data);

    switch (event.type) {
      case CreemEventType.ORDER_COMPLETED:
        return await handleOrderCompleted(event.data);
      
      case CreemEventType.SUBSCRIPTION_CREATED:
        return await handleSubscriptionCreated(event.data);
      
      case CreemEventType.SUBSCRIPTION_CANCELLED:
        return await handleSubscriptionCancelled(event.data);
      
      case CreemEventType.ORDER_FAILED:
        console.log('Order failed:', event.data.object);
        return { success: true, message: 'Order failed event logged' };
      
      default:
        console.log('Unhandled webhook event type:', event.type);
        return { success: true, message: 'Event type not handled' };
    }
  } catch (error) {
    console.error('Error processing webhook event:', error);
    return { success: false, error: error.message };
  }
};

// 验证Webhook签名（根据Creem.io文档）
export const verifyCreemSignature = (
  payload: string,
  signature: string,
  apiKey: string
): boolean => {
  try {
    // 根据Creem.io文档实现签名验证
    // 这里需要实际的签名验证逻辑
    console.log('Verifying webhook signature...');
    
    // 简单的验证示例（实际应用中需要更安全的实现）
    return signature && signature.length > 0;
  } catch (error) {
    console.error('Error verifying webhook signature:', error);
    return false;
  }
};

// 检查支付状态更新
export const checkPaymentStatusUpdates = () => {
  if (typeof window === 'undefined') return null;
  
  const updates = {
    paymentCompleted: localStorage.getItem('payment_completed') === 'true',
    subscriptionCreated: localStorage.getItem('subscription_created') === 'true',
    subscriptionCancelled: localStorage.getItem('subscription_cancelled') === 'true',
    orderId: localStorage.getItem('order_id'),
    subscriptionId: localStorage.getItem('subscription_id'),
    userId: localStorage.getItem('user_id')
  };
  
  // 清理localStorage
  if (updates.paymentCompleted) {
    localStorage.removeItem('payment_completed');
    localStorage.removeItem('order_id');
  }
  
  if (updates.subscriptionCreated) {
    localStorage.removeItem('subscription_created');
    localStorage.removeItem('subscription_id');
  }
  
  if (updates.subscriptionCancelled) {
    localStorage.removeItem('subscription_cancelled');
    localStorage.removeItem('cancelled_subscription_id');
  }
  
  if (updates.userId) {
    localStorage.removeItem('user_id');
  }
  
  return updates;
};

// 导出Webhook端点信息
export const CREEM_WEBHOOK_CONFIG = {
  endpoint: 'https://mystic-card-mirror-vip.lovable.app/api/webhooks/creem',
  events: [
    CreemEventType.ORDER_COMPLETED,
    CreemEventType.ORDER_FAILED,
    CreemEventType.SUBSCRIPTION_CREATED,
    CreemEventType.SUBSCRIPTION_UPDATED,
    CreemEventType.SUBSCRIPTION_CANCELLED,
    CreemEventType.SUBSCRIPTION_EXPIRED
  ],
  description: 'Mystic Card Mirror - Creem.io Payment Events'
};
