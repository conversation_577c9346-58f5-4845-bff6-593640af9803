// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://elwmthdxnsyzfvlunvmo.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsd210aGR4bnN5emZ2bHVudm1vIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxNzg5NjMsImV4cCI6MjA2Mzc1NDk2M30.bOQJV_fApPsev2ZCg5liq15rIhD7bzdrhWELM1BCGnk";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);