export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      daily_usage: {
        Row: {
          created_at: string
          date: string
          id: string
          reading_count: number
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          date?: string
          id?: string
          reading_count?: number
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          date?: string
          id?: string
          reading_count?: number
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      payment_history: {
        Row: {
          amount: number | null
          created_at: string | null
          currency: string | null
          id: string
          order_id: string | null
          payment_method: string | null
          status: string
          subscription_id: string | null
          user_id: string
        }
        Insert: {
          amount?: number | null
          created_at?: string | null
          currency?: string | null
          id?: string
          order_id?: string | null
          payment_method?: string | null
          status: string
          subscription_id?: string | null
          user_id: string
        }
        Update: {
          amount?: number | null
          created_at?: string | null
          currency?: string | null
          id?: string
          order_id?: string | null
          payment_method?: string | null
          status?: string
          subscription_id?: string | null
          user_id?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          id: string
          updated_at: string | null
          user_id: string | null
          username: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          id: string
          updated_at?: string | null
          user_id?: string | null
          username?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          id?: string
          updated_at?: string | null
          user_id?: string | null
          username?: string | null
        }
        Relationships: []
      }
      reading_feedback: {
        Row: {
          accuracy_rating: number | null
          created_at: string
          feedback_text: string | null
          id: string
          reading_id: string
          satisfaction_rating: number | null
          usefulness_rating: number | null
          user_id: string
        }
        Insert: {
          accuracy_rating?: number | null
          created_at?: string
          feedback_text?: string | null
          id?: string
          reading_id: string
          satisfaction_rating?: number | null
          usefulness_rating?: number | null
          user_id: string
        }
        Update: {
          accuracy_rating?: number | null
          created_at?: string
          feedback_text?: string | null
          id?: string
          reading_id?: string
          satisfaction_rating?: number | null
          usefulness_rating?: number | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "reading_feedback_reading_id_fkey"
            columns: ["reading_id"]
            isOneToOne: false
            referencedRelation: "reading_history"
            referencedColumns: ["id"]
          },
        ]
      }
      reading_history: {
        Row: {
          ai_reading: string
          cards: Json
          created_at: string | null
          id: string
          question: string
          spread_type: string | null
          user_id: string
        }
        Insert: {
          ai_reading: string
          cards: Json
          created_at?: string | null
          id?: string
          question: string
          spread_type?: string | null
          user_id: string
        }
        Update: {
          ai_reading?: string
          cards?: Json
          created_at?: string | null
          id?: string
          question?: string
          spread_type?: string | null
          user_id?: string
        }
        Relationships: []
      }
      subscription_history: {
        Row: {
          action: string
          amount: number | null
          created_at: string
          creem_transaction_id: string | null
          currency: string | null
          id: string
          user_id: string
        }
        Insert: {
          action: string
          amount?: number | null
          created_at?: string
          creem_transaction_id?: string | null
          currency?: string | null
          id?: string
          user_id: string
        }
        Update: {
          action?: string
          amount?: number | null
          created_at?: string
          creem_transaction_id?: string | null
          currency?: string | null
          id?: string
          user_id?: string
        }
        Relationships: []
      }
      tarot_readings: {
        Row: {
          ai_reading: string
          cards: Json
          created_at: string
          id: string
          question: string
          spread_type: string
          user_id: string
        }
        Insert: {
          ai_reading: string
          cards: Json
          created_at?: string
          id?: string
          question: string
          spread_type: string
          user_id: string
        }
        Update: {
          ai_reading?: string
          cards?: Json
          created_at?: string
          id?: string
          question?: string
          spread_type?: string
          user_id?: string
        }
        Relationships: []
      }
      user_subscriptions: {
        Row: {
          created_at: string | null
          current_period_end: string | null
          current_period_start: string | null
          daily_questions_limit: number | null
          daily_questions_used: number | null
          id: string
          last_question_date: string | null
          status: string
          subscription_end_date: string | null
          subscription_id: string | null
          subscription_start_date: string | null
          subscription_type: string
          trial_end_date: string | null
          trial_start_date: string | null
          trial_used: boolean | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          current_period_end?: string | null
          current_period_start?: string | null
          daily_questions_limit?: number | null
          daily_questions_used?: number | null
          id?: string
          last_question_date?: string | null
          status?: string
          subscription_end_date?: string | null
          subscription_id?: string | null
          subscription_start_date?: string | null
          subscription_type?: string
          trial_end_date?: string | null
          trial_start_date?: string | null
          trial_used?: boolean | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          current_period_end?: string | null
          current_period_start?: string | null
          daily_questions_limit?: number | null
          daily_questions_used?: number | null
          id?: string
          last_question_date?: string | null
          status?: string
          subscription_end_date?: string | null
          subscription_id?: string | null
          subscription_start_date?: string | null
          subscription_type?: string
          trial_end_date?: string | null
          trial_start_date?: string | null
          trial_used?: boolean | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      can_user_read_tarot: {
        Args: { user_uuid: string }
        Returns: boolean
      }
      record_tarot_usage: {
        Args: { user_uuid: string }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
