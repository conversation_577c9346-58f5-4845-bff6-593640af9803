
import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useLanguage } from './LanguageContext';

interface SubscriptionContextType {
  canAskQuestion: boolean;
  questionsRemaining: number;
  isTrialActive: boolean;
  isSubscriptionActive: boolean;
  daysRemainingInTrial: number;
  currentDailyLimit: number; // 添加当前实际的每日限制
  refreshSubscription: () => Promise<void>;
  startFreeTrial: () => Promise<void>;
  loading: boolean;
  subscription: any;
  checkSubscription: () => Promise<void>;
  upgradeToMonthly: (subscriptionId?: string, customerId?: string) => Promise<{ error?: any }>;
  checkUsageLimit: () => Promise<boolean>;
  incrementQuestionCount: () => Promise<void>;
  fixNewUserTrial: () => Promise<{ error?: any; success?: boolean }>; // 🔧 新增修复函数
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};

interface SubscriptionProviderProps {
  children: ReactNode;
}

export const SubscriptionProvider: React.FC<SubscriptionProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const { language } = useLanguage();
  const [canAskQuestion, setCanAskQuestion] = useState(false);
  const [questionsRemaining, setQuestionsRemaining] = useState(0);
  const [isTrialActive, setIsTrialActive] = useState(false);
  const [isSubscriptionActive, setIsSubscriptionActive] = useState(false);
  const [daysRemainingInTrial, setDaysRemainingInTrial] = useState(0);
  const [currentDailyLimit, setCurrentDailyLimit] = useState(1); // 添加当前实际限制状态
  const [loading, setLoading] = useState(true);
  const [subscription, setSubscription] = useState<any>(null);

  const checkSubscriptionStatus = async () => {
    if (!user) {
      setCanAskQuestion(false);
      setQuestionsRemaining(0);
      setIsTrialActive(false);
      setIsSubscriptionActive(false);
      setDaysRemainingInTrial(0);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // Get user's daily usage from reading_count column
      const { data: usageData, error: usageError } = await supabase
        .from('daily_usage')
        .select('reading_count')
        .eq('user_id', user.id)
        .eq('date', new Date().toISOString().split('T')[0])
        .single();

      if (usageError && usageError.code !== 'PGRST116') {
        console.error('Error fetching usage data:', usageError);
      }

      const currentUsage = usageData?.reading_count || 0;

      // Get user subscription from user_subscriptions table
      const { data: subscriptionData, error: subscriptionError } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (subscriptionError && subscriptionError.code !== 'PGRST116') {
        console.error('Error fetching subscription:', subscriptionError);
        setIsTrialActive(false);
        setIsSubscriptionActive(false);
        setDaysRemainingInTrial(0);
        setCurrentDailyLimit(1); // 无订阅时设为免费限制
        setQuestionsRemaining(1 - currentUsage);
        setSubscription(null);
        setCanAskQuestion((1 - currentUsage) > 0);
        return;
      }

      setSubscription(subscriptionData);

      // 🔧 修复订阅逻辑：支持多种订阅类型和状态判断
      const subscriptionType = subscriptionData?.subscription_type;
      const status = subscriptionData?.status;

      // 支持多种日期字段格式
      const startDate = subscriptionData?.current_period_start || subscriptionData?.trial_start_date || subscriptionData?.subscription_start_date;
      const endDate = subscriptionData?.current_period_end || subscriptionData?.trial_end_date || subscriptionData?.subscription_end_date;
      const subscriptionId = subscriptionData?.subscription_id;

      // 🔍 添加详细调试日志
      console.log('🔍 Subscription Debug Info:', {
        user_id: user?.id,
        user_email: user?.email,
        subscriptionType,
        status,
        startDate,
        endDate,
        subscriptionId,
        daily_questions_limit: subscriptionData?.daily_questions_limit,
        trial_used: subscriptionData?.trial_used,
        currentUsage,
        now: new Date().toISOString(),
        rawSubscriptionData: subscriptionData
      });

      // Calculate subscription status
      const now = new Date();
      const start = startDate ? new Date(startDate) : null;
      const end = endDate ? new Date(endDate) : null;

      let trialActive = false;
      let subscriptionActive = false;
      let daysRemaining = 0;
      let questionsLeft = 0;
      let limit = 1; // 默认免费用户限制

      // 🔧 改进的状态判断逻辑
      const isValidSubscription = status === 'active' && end && now <= end;

      if (isValidSubscription) {
        if (subscriptionType === 'trial' || subscriptionType === 'free_trial') {
          // 只有在没有付费订阅ID的情况下才显示试用期
          if (!subscriptionId) {
            trialActive = true;
            daysRemaining = Math.ceil((end.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
            limit = subscriptionData?.daily_questions_limit || 20; // 试用期使用数据库中的限制
          } else {
            // 有付费订阅ID，按付费会员处理
            subscriptionActive = true;
            limit = subscriptionData?.daily_questions_limit || 20; // 付费会员使用数据库中的限制
          }
        } else if (subscriptionType === 'monthly') {
          subscriptionActive = true;
          limit = subscriptionData?.daily_questions_limit || 20; // 付费会员使用数据库中的限制
        }

        questionsLeft = Math.max(0, limit - currentUsage);
        console.log('✅ Valid subscription found:', { trialActive, subscriptionActive, limit, questionsLeft });
      } else {
        // 🔧 订阅过期、无效或状态不是active，强制设为免费用户限制
        limit = 1; // 免费用户每天1次
        questionsLeft = Math.max(0, limit - currentUsage);
        console.log('⚠️ Invalid/expired subscription, using free limit:', {
          status,
          endDate,
          expired: end ? now > end : 'no_end_date',
          limit,
          questionsLeft
        });
      }

      setIsTrialActive(trialActive);
      setIsSubscriptionActive(subscriptionActive);
      setDaysRemainingInTrial(daysRemaining);
      setCurrentDailyLimit(limit); // 设置当前实际限制
      setQuestionsRemaining(Math.max(0, questionsLeft));
      setCanAskQuestion(questionsLeft > 0);

    } catch (error) {
      console.error('Error checking subscription status:', error);
      setCanAskQuestion(false);
      setQuestionsRemaining(0);
    } finally {
      setLoading(false);
    }
  };

  const checkUsageLimit = async (): Promise<boolean> => {
    const canUse = questionsRemaining > 0;
    console.log('🔍 checkUsageLimit called:', {
      questionsRemaining,
      canUse,
      isTrialActive,
      isSubscriptionActive,
      currentDailyLimit,
      user_email: user?.email
    });
    return canUse;
  };

  const incrementQuestionCount = async () => {
    if (!user) return;

    try {
      const today = new Date().toISOString().split('T')[0];

      // 🔧 先获取当前使用次数，然后递增
      const { data: currentUsage, error: fetchError } = await supabase
        .from('daily_usage')
        .select('reading_count')
        .eq('user_id', user.id)
        .eq('date', today)
        .single();

      const newCount = (currentUsage?.reading_count || 0) + 1;

      // Upsert daily usage with incremented count
      const { error } = await supabase
        .from('daily_usage')
        .upsert({
          user_id: user.id,
          date: today,
          reading_count: newCount
        }, {
          onConflict: 'user_id,date',
          ignoreDuplicates: false
        });

      if (error) {
        console.error('Error incrementing question count:', error);
        return;
      }

      // 🔧 立即更新本地状态，避免重新获取所有数据
      const newQuestionsRemaining = Math.max(0, questionsRemaining - 1);
      setQuestionsRemaining(newQuestionsRemaining);
      setCanAskQuestion(newQuestionsRemaining > 0);

      console.log('✅ Question count incremented:', {
        oldCount: (currentUsage?.reading_count || 0),
        newCount,
        questionsRemaining: newQuestionsRemaining
      });

    } catch (error) {
      console.error('Error incrementing question count:', error);
    }
  };

  const startFreeTrial = async () => {
    if (!user) return;

    try {
      const now = new Date();
      const endDate = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000); // 3 days from now

      const { error } = await supabase
        .from('user_subscriptions')
        .upsert({
          user_id: user.id,
          subscription_type: 'free_trial',
          status: 'active',
          daily_questions_limit: 20,
          daily_questions_used: 0,
          trial_start_date: now.toISOString(),
          trial_end_date: endDate.toISOString(),
          trial_used: true,
          created_at: now.toISOString(),
          updated_at: now.toISOString()
        });

      if (error) {
        console.error('Error starting free trial:', error);
        toast.error(language === 'en' ? 'Failed to start free trial' : '启动免费试用失败');
        return;
      }

      toast.success(language === 'en' ? 'Free trial started!' : '免费试用已开始！');
      await refreshSubscription();
    } catch (error) {
      console.error('Error starting free trial:', error);
      toast.error(language === 'en' ? 'Failed to start free trial' : '启动免费试用失败');
    }
  };

  // 🔧 新增：修复新用户试用期的函数
  const fixNewUserTrial = async () => {
    if (!user) return;

    try {
      console.log('🔧 Fixing trial for user:', user.email);

      const now = new Date();
      const endDate = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000); // 3 days from now

      const { error } = await supabase
        .from('user_subscriptions')
        .upsert({
          user_id: user.id,
          subscription_type: 'free_trial',
          status: 'active',
          daily_questions_limit: 20,
          daily_questions_used: 0,
          trial_start_date: now.toISOString(),
          trial_end_date: endDate.toISOString(),
          trial_used: true,
          created_at: now.toISOString(),
          updated_at: now.toISOString()
        }, {
          onConflict: 'user_id'
        });

      if (error) {
        console.error('Error fixing trial:', error);
        return { error };
      }

      console.log('✅ Trial fixed successfully');
      await refreshSubscription();
      return { success: true };
    } catch (error) {
      console.error('Error fixing trial:', error);
      return { error };
    }
  };

  const upgradeToMonthly = async (subscriptionId?: string, customerId?: string) => {
    if (!user) return { error: 'No user' };

    try {
      const now = new Date();
      const endDate = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());

      const { error } = await supabase
        .from('user_subscriptions')
        .upsert({
          user_id: user.id,
          subscription_type: 'monthly',
          current_period_start: now.toISOString(),
          current_period_end: endDate.toISOString(),
          daily_questions_limit: 50,
          status: 'active',
          subscription_id: subscriptionId
        });

      if (error) {
        console.error('Error upgrading to monthly:', error);
        return { error };
      }

      await checkSubscriptionStatus();
      return {};
    } catch (error) {
      console.error('Error upgrading to monthly:', error);
      return { error };
    }
  };

  const checkSubscription = async () => {
    await checkSubscriptionStatus();
  };

  const refreshSubscription = async () => {
    await checkSubscriptionStatus();
  };

  useEffect(() => {
    checkSubscriptionStatus();
  }, [user]);

  const value: SubscriptionContextType = {
    canAskQuestion,
    questionsRemaining,
    isTrialActive,
    isSubscriptionActive,
    daysRemainingInTrial,
    currentDailyLimit,
    refreshSubscription,
    startFreeTrial,
    loading,
    subscription,
    checkSubscription,
    upgradeToMonthly,
    checkUsageLimit,
    incrementQuestionCount,
    fixNewUserTrial, // 🔧 新增修复函数
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
};
