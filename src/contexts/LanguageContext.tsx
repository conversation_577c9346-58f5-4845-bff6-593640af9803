
import React, { createContext, useContext, useState } from 'react';

type Language = 'en' | 'zh';

type LanguageContextType = {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
};

const translations = {
  en: {
    'nav.home': 'Home',

    'welcome.title': 'Mystic Mirror',
    'welcome.subtitle': 'Reveal insights about your past, present, and future through the ancient wisdom of tarot',
    'welcome.start': 'Begin Your Reading',
    'rates.15min': '15 minutes - $30 / ¥210',
    'rates.30min': '30 minutes - $50 / ¥350',
    'rates.60min': '60 minutes - $80 / ¥560',
    'contact.wechat': 'WeChat: zhidasuc',
    'contact.instagram': 'Instagram: mystic_mirror_vip',
    'contact.button': 'Contact Now',
    'reader.experience': '10+ Years of Tarot Reading Experience',
    'reader.speciality': 'Professional Tarot Reading',
    'reading.question': 'Your Question:',
    'reading.cards': 'Cards Drawn:',
    'reading.interpretation': 'Tarot Interpretation',
    'reading.loading': 'Generating your personalized tarot reading...',
    'reading.processing': 'Interpretation in progress, please quietly contemplate your question in your heart~',
    'reading.new': 'Start New Reading',
    'position.upright': 'Upright',
    'position.reversed': 'Reversed',
  },
  zh: {
    'nav.home': '首页',

    'welcome.title': '神秘镜像',
    'welcome.subtitle': '通过古老的塔罗智慧，揭示你的过去、现在和未来',
    'welcome.start': '开始占卜',
    'rates.15min': '15分钟 - ¥210 / $30',
    'rates.30min': '30分钟 - ¥350 / $50',
    'rates.60min': '60分钟 - ¥560 / $80',
    'contact.wechat': '微信: zhidasuc',
    'contact.instagram': 'Instagram: mystic_mirror_vip',
    'contact.button': '立即联系',
    'reader.experience': '10年以上塔罗占卜经验',
    'reader.speciality': '专业塔罗占卜',
    'reading.question': '您的问题：',
    'reading.cards': '抽取的卡牌：',
    'reading.interpretation': '塔罗牌解读',
    'reading.loading': '正在生成您的个人塔罗牌解读...',
    'reading.processing': '解读生成中，请在心中默默思考自己的问题～',
    'reading.new': '开始新的解读',
    'position.upright': '正位',
    'position.reversed': '逆位',
  }
};

export const LanguageContext = createContext<LanguageContextType>({
  language: 'en',
  setLanguage: () => {},
  t: () => '',
});

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<Language>('en');

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations['en']] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => useContext(LanguageContext);
