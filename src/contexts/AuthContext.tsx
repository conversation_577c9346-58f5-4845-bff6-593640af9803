
import React, { createContext, useContext, useState, useEffect } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signUp: (email: string, password: string, username?: string) => Promise<{ error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  deleteAccount: () => Promise<{ error: any }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 设置认证状态监听器
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('🔐 Auth state changed:', event, session?.user?.email);
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);
      }
    );

    // 检查现有会话
    supabase.auth.getSession().then(({ data: { session } }) => {
      console.log('🔐 Current session:', session?.user?.email);
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signUp = async (email: string, password: string, username?: string) => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${window.location.origin}/`,
        data: {
          username: username || email.split('@')[0]
        }
      }
    });
    return { error };
  };

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    return { error };
  };

  const signOut = async () => {
    console.log('🚪 Signing out user');
    await supabase.auth.signOut();
  };

  const deleteAccount = async () => {
    if (!user || !session) return { error: new Error('No user logged in') };

    try {
      console.log('🗑️ Starting complete account deletion for user:', user.id);

      // 🔧 尝试通过后端API删除用户账户（包括认证记录）
      try {
        console.log('🔧 Attempting complete deletion via Netlify function...');

        // 首先测试函数是否可用
        console.log('🧪 Testing function availability...');
        const testResponse = await fetch('/.netlify/functions/delete-user', {
          method: 'GET'
        });

        console.log('📡 Test response status:', testResponse.status);
        const testText = await testResponse.text();
        console.log('📡 Test response text:', testText.substring(0, 200) + '...');

        if (testResponse.status !== 200 || testText.includes('<!DOCTYPE')) {
          throw new Error('Netlify function not available - got HTML instead of JSON');
        }

        // 如果测试通过，执行实际删除
        console.log('✅ Function available, proceeding with deletion...');
        const response = await fetch('/.netlify/functions/delete-user', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            user_id: user.id,
            auth_token: session.access_token
          })
        });

        console.log('📡 Backend API response status:', response.status);
        const responseText = await response.text();
        console.log('📡 Backend API response text:', responseText.substring(0, 200) + '...');

        if (response.ok && !responseText.includes('<!DOCTYPE')) {
          const result = JSON.parse(responseText);
          console.log('📡 Backend API response:', result);
          if (result.success) {
            console.log('✅ Account completely deleted via backend API');
            // 账户已完全删除，不需要手动登出，因为认证记录已被删除
            return { error: null };
          } else {
            console.warn('⚠️ Backend deletion returned success=false:', result);
          }
        } else {
          console.warn('⚠️ Backend deletion failed - got HTML response or error status');
          throw new Error('Backend API returned HTML instead of JSON');
        }

        console.log('🔄 Falling back to frontend deletion...');
      } catch (apiError) {
        console.warn('⚠️ Backend API error, using frontend deletion:', apiError);
      }

      // 🔧 回退：前端删除用户数据
      console.log('🗑️ Using frontend deletion as fallback...');

      // 删除用户的所有相关数据
      console.log('🗑️ Deleting reading feedback...');
      await supabase.from('reading_feedback').delete().eq('user_id', user.id);

      console.log('🗑️ Deleting reading history...');
      await supabase.from('reading_history').delete().eq('user_id', user.id);

      console.log('🗑️ Deleting tarot readings...');
      await supabase.from('tarot_readings').delete().eq('user_id', user.id);

      console.log('🗑️ Deleting payment history...');
      await supabase.from('payment_history').delete().eq('user_id', user.id);

      console.log('🗑️ Deleting user subscription...');
      await supabase.from('user_subscriptions').delete().eq('user_id', user.id);

      console.log('🗑️ Deleting user profile...');
      await supabase.from('profiles').delete().eq('user_id', user.id);

      console.log('🗑️ Deleting daily usage records...');
      await supabase.from('daily_usage').delete().eq('user_id', user.id);

      // 注意：前端无法删除auth.users，但可以注销用户
      console.log('⚠️ Cannot delete auth user from frontend, signing out instead');
      await supabase.auth.signOut();

      console.log('✅ User data deleted and signed out');
      return { error: null };
    } catch (error) {
      console.error('❌ Error during account deletion:', error);
      return { error };
    }
  };

  const value = {
    user,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    deleteAccount
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
