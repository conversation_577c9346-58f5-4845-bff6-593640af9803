-- 创建完全删除用户的函数
-- 这个函数需要在Supabase中执行，具有删除auth.users的权限

-- 删除用户的完整函数
CREATE OR REPLACE FUNCTION public.delete_user_completely(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- 删除用户相关的所有数据
  
  -- 1. 删除占卜反馈
  DELETE FROM public.reading_feedback WHERE user_id = delete_user_completely.user_id;
  
  -- 2. 删除占卜历史
  DELETE FROM public.reading_history WHERE user_id = delete_user_completely.user_id;
  
  -- 3. 删除塔罗占卜记录（如果存在）
  DELETE FROM public.tarot_readings WHERE user_id = delete_user_completely.user_id;
  
  -- 4. 删除支付历史
  DELETE FROM public.payment_history WHERE user_id = delete_user_completely.user_id;
  
  -- 5. 删除用户订阅
  DELETE FROM public.user_subscriptions WHERE user_id = delete_user_completely.user_id;
  
  -- 6. 删除用户资料
  DELETE FROM public.profiles WHERE user_id = delete_user_completely.user_id;
  
  -- 7. 最后删除认证用户（这需要特殊权限）
  -- 注意：这个操作需要service role权限
  DELETE FROM auth.users WHERE id = delete_user_completely.user_id;
  
  RETURN TRUE;
  
EXCEPTION
  WHEN OTHERS THEN
    -- 记录错误但不抛出异常
    RAISE WARNING 'Error deleting user %: %', user_id, SQLERRM;
    RETURN FALSE;
END;
$$;

-- 授予执行权限
GRANT EXECUTE ON FUNCTION public.delete_user_completely(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.delete_user_completely(UUID) TO service_role;

-- 创建一个更安全的版本，只删除用户数据但不删除auth.users
CREATE OR REPLACE FUNCTION public.delete_user_data_only(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- 只删除用户数据，不删除认证记录
  
  DELETE FROM public.reading_feedback WHERE user_id = delete_user_data_only.user_id;
  DELETE FROM public.reading_history WHERE user_id = delete_user_data_only.user_id;
  DELETE FROM public.tarot_readings WHERE user_id = delete_user_data_only.user_id;
  DELETE FROM public.payment_history WHERE user_id = delete_user_data_only.user_id;
  DELETE FROM public.user_subscriptions WHERE user_id = delete_user_data_only.user_id;
  DELETE FROM public.profiles WHERE user_id = delete_user_data_only.user_id;
  
  RETURN TRUE;
  
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Error deleting user data %: %', user_id, SQLERRM;
    RETURN FALSE;
END;
$$;

-- 授予执行权限
GRANT EXECUTE ON FUNCTION public.delete_user_data_only(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.delete_user_data_only(UUID) TO service_role;
