-- 更新订阅类型以支持新的一次性付费逻辑
-- 添加 'free' 订阅类型，移除不需要的约束

-- 1. 更新subscription_type的CHECK约束，添加'free'类型
ALTER TABLE public.user_subscriptions 
DROP CONSTRAINT IF EXISTS user_subscriptions_subscription_type_check;

ALTER TABLE public.user_subscriptions 
ADD CONSTRAINT user_subscriptions_subscription_type_check 
CHECK (subscription_type IN ('free', 'free_trial', 'monthly'));

-- 2. 更新status的CHECK约束，移除'cancelled'状态（因为采用一次性付费）
ALTER TABLE public.user_subscriptions 
DROP CONSTRAINT IF EXISTS user_subscriptions_status_check;

ALTER TABLE public.user_subscriptions 
ADD CONSTRAINT user_subscriptions_status_check 
CHECK (status IN ('active', 'expired'));

-- 3. 确保trial_used字段存在
ALTER TABLE public.user_subscriptions 
ADD COLUMN IF NOT EXISTS trial_used BOOLEAN DEFAULT FALSE;

-- 4. 确保customer_id字段存在
ALTER TABLE public.user_subscriptions 
ADD COLUMN IF NOT EXISTS customer_id VARCHAR(255);

-- 5. 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_trial_used 
ON public.user_subscriptions(trial_used);

CREATE INDEX IF NOT EXISTS idx_user_subscriptions_customer_id 
ON public.user_subscriptions(customer_id);

-- 6. 更新现有的'cancelled'状态记录为'expired'
UPDATE public.user_subscriptions 
SET status = 'expired' 
WHERE status = 'cancelled';

-- 7. 添加注释说明新的订阅逻辑
COMMENT ON COLUMN public.user_subscriptions.subscription_type 
IS 'Subscription type: free (default), free_trial (3-day trial), monthly (one-time monthly payment)';

COMMENT ON COLUMN public.user_subscriptions.status 
IS 'Subscription status: active (currently valid), expired (needs renewal)';

COMMENT ON COLUMN public.user_subscriptions.trial_used 
IS 'Whether user has used their one-time 3-day trial';

COMMENT ON COLUMN public.user_subscriptions.customer_id 
IS 'Creem.io customer ID for Customer Portal access';

-- 8. 验证更新
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'user_subscriptions' 
AND column_name IN ('subscription_type', 'status', 'trial_used', 'customer_id')
ORDER BY column_name;
