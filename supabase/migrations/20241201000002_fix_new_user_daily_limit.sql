-- 修复新用户注册时的每日提问限制
-- 新用户应该获得3天试用期，每天20次提问

-- 1. 更新数据库函数，让新用户获得3天试用期和20次每日限制
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    trial_start_date TIMESTAMPTZ;
    trial_end_date TIMESTAMPTZ;
BEGIN
    -- 设置3天试用期
    trial_start_date := NOW();
    trial_end_date := NOW() + INTERVAL '3 days';

    INSERT INTO public.user_subscriptions (
        user_id,
        subscription_type,
        status,
        daily_questions_limit,
        daily_questions_used,
        trial_start_date,
        trial_end_date,
        trial_used
    ) VALUES (
        NEW.id,
        'free_trial',  -- 新用户获得3天试用
        'active',
        20,  -- 🔧 新用户每天20次提问
        0,
        trial_start_date,
        trial_end_date,
        TRUE  -- 标记已使用试用期
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. 确保现有的免费用户有正确的1次每日限制
UPDATE public.user_subscriptions
SET
    daily_questions_limit = 1,
    updated_at = NOW()
WHERE
    subscription_type = 'free'
    AND daily_questions_limit != 1;

-- 3. 更新现有的试用用户，确保他们有20次每日限制
UPDATE public.user_subscriptions
SET
    daily_questions_limit = 20,
    updated_at = NOW()
WHERE
    subscription_type = 'free_trial'
    AND daily_questions_limit != 20;

-- 4. 为没有试用期日期的试用用户添加日期
UPDATE public.user_subscriptions
SET
    trial_start_date = created_at,
    trial_end_date = created_at + INTERVAL '3 days',
    updated_at = NOW()
WHERE
    subscription_type = 'free_trial'
    AND (trial_start_date IS NULL OR trial_end_date IS NULL);

-- 5. 添加注释说明新的逻辑
COMMENT ON FUNCTION public.handle_new_user()
IS 'Creates 3-day trial subscription with 20 daily questions for new users';

-- 6. 验证更新结果
SELECT
    subscription_type,
    daily_questions_limit,
    COUNT(*) as user_count
FROM public.user_subscriptions
GROUP BY subscription_type, daily_questions_limit
ORDER BY subscription_type, daily_questions_limit;
