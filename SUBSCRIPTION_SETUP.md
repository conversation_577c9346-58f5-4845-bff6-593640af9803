# 神秘卡牌占卜 - 订阅系统设置指南

## 概述

本指南将帮助您设置完整的订阅系统，包括：
1. 替换AI API为Hugging Face
2. 实现会员订阅逻辑（3天免费试用 + 每月$4.99）
3. 集成Creem.io支付系统
4. 添加使用限制（免费用户每天1个问题）

## 1. 数据库设置

### 在Supabase中执行SQL

1. 登录您的Supabase项目
2. 进入SQL编辑器
3. 执行 `database_schema.sql` 文件中的所有SQL语句

这将创建以下表：
- `user_subscriptions` - 用户订阅状态
- `payment_history` - 支付历史记录
- `reading_history` - 占卜历史记录（高级用户功能）

## 2. API配置

### Hugging Face API
- 已配置使用您的API密钥：`*************************************`
- 模型：`deepseek-ai/DeepSeek-R1`
- 文件位置：`src/utils/api/config.ts` 和 `src/utils/api/deepseekClient.ts`

### Creem.io API
- 已配置使用您的API密钥：`creem_2gPDtvmta7jlXaykq0H9Is`
- 文件位置：`src/utils/api/config.ts` 和 `src/utils/payment.ts`

## 3. Webhooks设置

### Creem.io Webhook配置

在您的Creem.io仪表板中添加以下Webhook：

**Webhook URL：**
```
https://your-domain.com/api/webhooks/creem
```

**事件类型：**
- `checkout.session.completed`
- `checkout.session.failed`
- `subscription.cancelled`
- `payment.succeeded`
- `payment.failed`

**HTTP方法：** POST
**内容类型：** application/json

### Webhook处理

Webhook处理逻辑位于 `src/api/webhooks.ts`。这个文件包含：
- 支付成功处理
- 支付失败处理
- 订阅取消处理
- 签名验证（安全性）

## 4. 新增页面和功能

### 新增页面
1. **定价页面** (`/pricing`) - 显示套餐选项和开始试用
2. **支持页面** (`/support`) - 客户支持和联系信息
3. **支付成功页面** (`/payment/success`) - 支付完成后的确认页面
4. **支付取消页面** (`/payment/cancel`) - 支付取消后的页面

### 订阅功能
- 3天免费试用
- 每月$4.99订阅
- 免费用户每天1个问题限制
- 付费用户无限制访问

## 5. 使用限制实现

### 免费用户限制
- 每天只能问1个问题
- 问题计数在每天午夜重置
- 超出限制时显示升级提示

### 试用期用户
- 3天内无限制访问
- 试用期结束后自动转为免费用户

### 付费用户
- 无限制访问所有功能
- 优先支持
- 占卜历史记录

## 6. 部署步骤

### 1. 安装依赖
```bash
npm install
```

### 2. 环境变量设置
确保以下环境变量已正确设置：
- Supabase配置
- Hugging Face API密钥
- Creem.io API密钥

### 3. 数据库迁移
在Supabase中执行 `database_schema.sql`

### 4. 部署应用
```bash
npm run build
npm run deploy
```

## 7. 测试流程

### 测试订阅流程
1. 注册新用户
2. 开始免费试用
3. 测试无限制访问
4. 试用期结束后测试限制
5. 测试升级到付费版本

### 测试支付流程
1. 点击升级按钮
2. 重定向到Creem.io支付页面
3. 完成支付
4. 验证Webhook处理
5. 确认订阅状态更新

## 8. 监控和维护

### 日志监控
- 检查API调用日志
- 监控支付状态
- 跟踪用户使用情况

### 数据库维护
- 定期清理过期数据
- 监控订阅状态
- 备份重要数据

## 9. 客户支持

### 联系方式
- **邮箱：** <EMAIL>
- **微信：** zhidasuc
- **Instagram：** @mystic_mirror_vip

### 常见问题
- 3天免费试用说明
- 支付问题处理
- 订阅取消流程
- 技术支持

## 10. 安全考虑

### API安全
- 所有API密钥都应该在服务器端存储
- 使用HTTPS进行所有通信
- 验证Webhook签名

### 用户数据保护
- 遵循GDPR和其他隐私法规
- 加密敏感数据
- 定期安全审计

## 11. 下一步优化

### 功能增强
- 添加年度订阅选项
- 实现推荐系统
- 添加社交分享功能

### 性能优化
- 缓存常用数据
- 优化API调用
- 实现CDN

## 访问网站

部署完成后，您可以通过以下方式访问更新后的网站：

1. **开发环境：** `http://localhost:3000`
2. **生产环境：** 您的部署域名

所有新功能都已集成到现有的网站中，用户可以无缝体验新的订阅系统。

## 支持

如果您在设置过程中遇到任何问题，请参考：
1. 检查控制台错误日志
2. 验证API密钥配置
3. 确认数据库表已正确创建
4. 测试Webhook端点连接

祝您使用愉快！🔮✨
