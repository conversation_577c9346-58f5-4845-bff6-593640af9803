-- Complete user deletion function
-- This function ensures all user data is completely removed from the database

CREATE OR REPLACE FUNCTION complete_user_deletion(target_user_id UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    deletion_results JSON;
    feedback_count INTEGER;
    history_count INTEGER;
    tarot_count INTEGER;
    payment_count INTEGER;
    subscription_count INTEGER;
    profile_count INTEGER;
BEGIN
    -- Log the deletion attempt
    RAISE NOTICE 'Starting complete deletion for user: %', target_user_id;
    
    -- Delete reading_feedback
    DELETE FROM reading_feedback WHERE user_id = target_user_id;
    GET DIAGNOSTICS feedback_count = ROW_COUNT;
    RAISE NOTICE 'Deleted % reading_feedback records', feedback_count;
    
    -- Delete reading_history
    DELETE FROM reading_history WHERE user_id = target_user_id;
    GET DIAGNOSTICS history_count = ROW_COUNT;
    RAISE NOTICE 'Deleted % reading_history records', history_count;
    
    -- Delete tarot_readings
    DELETE FROM tarot_readings WHERE user_id = target_user_id;
    GET DIAGNOSTICS tarot_count = ROW_COUNT;
    RAISE NOTICE 'Deleted % tarot_readings records', tarot_count;
    
    -- Delete payment_history
    DELETE FROM payment_history WHERE user_id = target_user_id;
    GET DIAGNOSTICS payment_count = ROW_COUNT;
    RAISE NOTICE 'Deleted % payment_history records', payment_count;
    
    -- Delete user_subscriptions
    DELETE FROM user_subscriptions WHERE user_id = target_user_id;
    GET DIAGNOSTICS subscription_count = ROW_COUNT;
    RAISE NOTICE 'Deleted % user_subscriptions records', subscription_count;
    
    -- Delete profiles
    DELETE FROM profiles WHERE user_id = target_user_id;
    GET DIAGNOSTICS profile_count = ROW_COUNT;
    RAISE NOTICE 'Deleted % profiles records', profile_count;
    
    -- Build result JSON
    deletion_results := json_build_object(
        'reading_feedback', feedback_count,
        'reading_history', history_count,
        'tarot_readings', tarot_count,
        'payment_history', payment_count,
        'user_subscriptions', subscription_count,
        'profiles', profile_count,
        'total_records_deleted', feedback_count + history_count + tarot_count + payment_count + subscription_count + profile_count,
        'user_id', target_user_id,
        'deleted_at', NOW()
    );
    
    RAISE NOTICE 'Complete deletion finished. Results: %', deletion_results;
    
    RETURN deletion_results;
END;
$$;

-- Grant execute permission to service role
GRANT EXECUTE ON FUNCTION complete_user_deletion(UUID) TO service_role;

-- Create a view to check user data before deletion (for debugging)
CREATE OR REPLACE VIEW user_data_summary AS
SELECT 
    u.id as user_id,
    u.email,
    u.created_at as user_created_at,
    (SELECT COUNT(*) FROM reading_feedback rf WHERE rf.user_id = u.id) as feedback_count,
    (SELECT COUNT(*) FROM reading_history rh WHERE rh.user_id = u.id) as history_count,
    (SELECT COUNT(*) FROM tarot_readings tr WHERE tr.user_id = u.id) as tarot_count,
    (SELECT COUNT(*) FROM payment_history ph WHERE ph.user_id = u.id) as payment_count,
    (SELECT COUNT(*) FROM user_subscriptions us WHERE us.user_id = u.id) as subscription_count,
    (SELECT COUNT(*) FROM profiles p WHERE p.user_id = u.id) as profile_count
FROM auth.users u;

-- Grant select permission to service role
GRANT SELECT ON user_data_summary TO service_role;

-- Test function (for development only)
-- SELECT complete_user_deletion('test-user-id');
